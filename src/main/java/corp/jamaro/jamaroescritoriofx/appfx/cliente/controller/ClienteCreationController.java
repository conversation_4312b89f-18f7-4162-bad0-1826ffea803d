package corp.jamaro.jamaroescritoriofx.appfx.cliente.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.Cliente;
import corp.jamaro.jamaroescritoriofx.appfx.service.ClienteService;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.CustomTextField;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.util.ResourceBundle;

/**
 * Controlador reutilizable para la creación y edición de clientes.
 * Utiliza handlers funcionales para permitir diferentes comportamientos según el contexto de uso.
 */
@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class ClienteCreationController extends BaseController {

    // ===== Servicios inyectados =====
    private final ClienteService clienteService;
    private final AlertUtil alertUtil;

    // ===== Controles FXML =====
    @FXML private StackPane stackPaneClienteCreation;
    @FXML private AnchorPane anchorPaneMain;
    @FXML private ScrollPane scrollPaneForm;
    @FXML private VBox vboxForm;
    @FXML private HBox hboxButtons;
    
    @FXML private Label lblTitulo;
    
    // Campos de información personal
    @FXML private CustomTextField txtNombre;
    @FXML private CustomTextField txtApellido;
    @FXML private CustomTextField txtRazonSocial;
    
    // Campos de documentos
    @FXML private CustomTextField txtDni;
    @FXML private CustomTextField txtRuc;
    @FXML private CustomTextField txtOtroDocumento;
    
    // Campos de contacto
    @FXML private CustomTextField txtDireccion;
    @FXML private CustomTextField txtTelefono;
    @FXML private CustomTextField txtEmail;
    
    // Configuraciones
    @FXML private CheckBox chkTieneCredito;
    @FXML private CheckBox chkEsMayorista;
    @FXML private CheckBox chkEstado;
    
    // Metadata
    @FXML private TextArea txtMetadata;
    
    // Botones
    @FXML private Button btnCancelar;
    @FXML private Button btnGuardar;
    
    // Indicador de carga
    @FXML private ProgressIndicator loadingIndicator;

    // ===== Handlers funcionales =====
    private ClienteCreatedHandler clienteCreatedHandler;
    private ClienteUpdatedHandler clienteUpdatedHandler;
    private CancelHandler cancelHandler;
    private ErrorHandler errorHandler;

    // ===== Estado del controlador =====
    private Cliente clienteToEdit; // null para crear nuevo, objeto para editar existente
    private String initialInputText; // texto inicial que disparó la creación

    // ===== Interfaces funcionales =====
    
    /**
     * Handler llamado cuando se crea exitosamente un nuevo cliente
     */
    @FunctionalInterface
    public interface ClienteCreatedHandler {
        void handle(Cliente cliente, String originalInputText);
    }

    /**
     * Handler llamado cuando se actualiza exitosamente un cliente existente
     */
    @FunctionalInterface
    public interface ClienteUpdatedHandler {
        void handle(Cliente cliente);
    }

    /**
     * Handler llamado cuando el usuario cancela la operación
     */
    @FunctionalInterface
    public interface CancelHandler {
        void handle();
    }

    /**
     * Handler llamado cuando ocurre un error
     */
    @FunctionalInterface
    public interface ErrorHandler {
        void handle(String errorMessage, Throwable error);
    }

    // ===== Métodos de configuración =====

    /**
     * Configura el controlador para crear un nuevo cliente
     * @param inputText Texto inicial que disparó la creación (documento o nombre)
     * @param isNumeric Si el texto es numérico (documento) o no (nombre)
     */
    public void configureForNewCliente(String inputText, boolean isNumeric) {
        this.clienteToEdit = null;
        this.initialInputText = inputText;
        
        runOnUiThread(() -> {
            lblTitulo.setText("Crear Nuevo Cliente");
            btnGuardar.setText("Crear");
            
            // Pre-llenar campos según el tipo de entrada
            if (isNumeric) {
                // Es un documento, intentar determinar el tipo
                if (inputText.length() == 8) {
                    txtDni.setText(inputText);
                } else if (inputText.length() == 11) {
                    txtRuc.setText(inputText);
                } else {
                    txtOtroDocumento.setText(inputText);
                }
            } else {
                // Es texto, asumir que es nombre
                txtNombre.setText(inputText);
            }
            
            // Limpiar otros campos
            clearAllFieldsExceptPrefilled();
        });
        
        log.info("Configurado para crear nuevo cliente con entrada: '{}' (numérico: {})", inputText, isNumeric);
    }

    /**
     * Configura el controlador para editar un cliente existente
     * @param cliente Cliente a editar
     */
    public void configureForEditCliente(Cliente cliente) {
        this.clienteToEdit = cliente;
        this.initialInputText = null;
        
        runOnUiThread(() -> {
            lblTitulo.setText("Editar Cliente");
            btnGuardar.setText("Actualizar");
            
            // Llenar todos los campos con los datos del cliente
            fillFieldsFromCliente(cliente);
        });
        
        log.info("Configurado para editar cliente: {} {}", cliente.getNombre(), cliente.getApellido());
    }

    /**
     * Establece el handler para cuando se crea un cliente exitosamente
     */
    public void setClienteCreatedHandler(ClienteCreatedHandler handler) {
        this.clienteCreatedHandler = handler;
    }

    /**
     * Establece el handler para cuando se actualiza un cliente exitosamente
     */
    public void setClienteUpdatedHandler(ClienteUpdatedHandler handler) {
        this.clienteUpdatedHandler = handler;
    }

    /**
     * Establece el handler para cuando se cancela la operación
     */
    public void setCancelHandler(CancelHandler handler) {
        this.cancelHandler = handler;
    }

    /**
     * Establece el handler para cuando ocurre un error
     */
    public void setErrorHandler(ErrorHandler handler) {
        this.errorHandler = handler;
    }

    // ===== Métodos FXML =====

    @FXML
    private void handleGuardar() {
        if (!validateFields()) {
            return;
        }

        Cliente cliente = createClienteFromFields();
        
        if (clienteToEdit == null) {
            // Crear nuevo cliente
            createNewCliente(cliente);
        } else {
            // Actualizar cliente existente
            updateExistingCliente(cliente);
        }
    }

    @FXML
    private void handleCancelar() {
        log.debug("Usuario canceló la operación de cliente");
        if (cancelHandler != null) {
            cancelHandler.handle();
        }
    }

    // ===== Métodos de inicialización =====

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        log.debug("Inicializando ClienteCreationController");
        
        // Configurar validaciones en tiempo real si es necesario
        setupFieldValidations();
        
        // Configurar el indicador de carga
        loadingIndicator.setVisible(false);
        loadingIndicator.setMouseTransparent(true);
    }

    // ===== Métodos privados =====

    private void setupFieldValidations() {
        // Aquí se pueden agregar validaciones en tiempo real
        // Por ejemplo, validar formato de email, longitud de documentos, etc.
    }

    private boolean validateFields() {
        // Validar que al menos nombre o razón social estén llenos
        String nombre = txtNombre.getText().trim();
        String apellido = txtApellido.getText().trim();
        String razonSocial = txtRazonSocial.getText().trim();
        
        if (nombre.isEmpty() && razonSocial.isEmpty()) {
            showError("Debe ingresar al menos el nombre o la razón social del cliente");
            return false;
        }
        
        // Validar que al menos un documento esté lleno
        String dni = txtDni.getText().trim();
        String ruc = txtRuc.getText().trim();
        String otroDoc = txtOtroDocumento.getText().trim();
        
        if (dni.isEmpty() && ruc.isEmpty() && otroDoc.isEmpty()) {
            showError("Debe ingresar al menos un documento del cliente");
            return false;
        }
        
        return true;
    }

    private Cliente createClienteFromFields() {
        Cliente cliente = new Cliente();
        
        // Si estamos editando, mantener el ID
        if (clienteToEdit != null) {
            cliente.setId(clienteToEdit.getId());
        }
        
        // Información personal
        cliente.setNombre(getTextOrNull(txtNombre));
        cliente.setApellido(getTextOrNull(txtApellido));
        cliente.setRazonSocial(getTextOrNull(txtRazonSocial));
        
        // Documentos
        cliente.setDni(getTextOrNull(txtDni));
        cliente.setRuc(getTextOrNull(txtRuc));
        cliente.setOtroDocumento(getTextOrNull(txtOtroDocumento));
        
        // Contacto
        cliente.setDireccion(getTextOrNull(txtDireccion));
        cliente.setTelefono(getTextOrNull(txtTelefono));
        cliente.setEmail(getTextOrNull(txtEmail));
        
        // Configuraciones
        cliente.setTieneCredito(chkTieneCredito.isSelected());
        cliente.setEsMayorista(chkEsMayorista.isSelected());
        cliente.setEstado(chkEstado.isSelected());
        
        // Metadata
        cliente.setMetadata(getTextOrNull(txtMetadata));
        
        return cliente;
    }

    private String getTextOrNull(CustomTextField field) {
        String text = field.getText().trim();
        return text.isEmpty() ? null : text;
    }

    private String getTextOrNull(TextArea field) {
        String text = field.getText().trim();
        return text.isEmpty() ? null : text;
    }

    private void createNewCliente(Cliente cliente) {
        log.info("Creando nuevo cliente: {} {}", cliente.getNombre(), cliente.getApellido());
        
        loadingIndicator.setVisible(true);
        subscribeOnBoundedElastic(
                clienteService.createCliente(cliente),
                createdCliente -> {
                    loadingIndicator.setVisible(false);
                    log.info("Cliente creado exitosamente: {}", createdCliente.getId());
                    
                    runOnUiThread(() -> {
                        if (clienteCreatedHandler != null) {
                            clienteCreatedHandler.handle(createdCliente, initialInputText);
                        }
                    });
                },
                error -> {
                    loadingIndicator.setVisible(false);
                    log.error("Error al crear cliente: {}", error.getMessage(), error);
                    
                    runOnUiThread(() -> {
                        if (errorHandler != null) {
                            errorHandler.handle("Error al crear cliente: " + error.getMessage(), error);
                        } else {
                            showError("Error al crear cliente: " + error.getMessage());
                        }
                    });
                }
        );
    }

    private void updateExistingCliente(Cliente cliente) {
        log.info("Actualizando cliente existente: {}", cliente.getId());
        
        loadingIndicator.setVisible(true);
        subscribeOnBoundedElastic(
                clienteService.updateCliente(cliente.getId(), cliente),
                updatedCliente -> {
                    loadingIndicator.setVisible(false);
                    log.info("Cliente actualizado exitosamente: {}", updatedCliente.getId());
                    
                    runOnUiThread(() -> {
                        if (clienteUpdatedHandler != null) {
                            clienteUpdatedHandler.handle(updatedCliente);
                        }
                    });
                },
                error -> {
                    loadingIndicator.setVisible(false);
                    log.error("Error al actualizar cliente: {}", error.getMessage(), error);
                    
                    runOnUiThread(() -> {
                        if (errorHandler != null) {
                            errorHandler.handle("Error al actualizar cliente: " + error.getMessage(), error);
                        } else {
                            showError("Error al actualizar cliente: " + error.getMessage());
                        }
                    });
                }
        );
    }

    private void clearAllFieldsExceptPrefilled() {
        // No limpiar los campos que ya fueron pre-llenados
        // Solo limpiar los campos que están vacíos
        if (txtNombre.getText().trim().isEmpty()) txtNombre.clear();
        if (txtApellido.getText().trim().isEmpty()) txtApellido.clear();
        if (txtRazonSocial.getText().trim().isEmpty()) txtRazonSocial.clear();
        if (txtDni.getText().trim().isEmpty()) txtDni.clear();
        if (txtRuc.getText().trim().isEmpty()) txtRuc.clear();
        if (txtOtroDocumento.getText().trim().isEmpty()) txtOtroDocumento.clear();
        
        txtDireccion.clear();
        txtTelefono.clear();
        txtEmail.clear();
        txtMetadata.clear();
        
        chkTieneCredito.setSelected(false);
        chkEsMayorista.setSelected(false);
        chkEstado.setSelected(true); // Por defecto activo
    }

    private void fillFieldsFromCliente(Cliente cliente) {
        txtNombre.setText(cliente.getNombre() != null ? cliente.getNombre() : "");
        txtApellido.setText(cliente.getApellido() != null ? cliente.getApellido() : "");
        txtRazonSocial.setText(cliente.getRazonSocial() != null ? cliente.getRazonSocial() : "");
        
        txtDni.setText(cliente.getDni() != null ? cliente.getDni() : "");
        txtRuc.setText(cliente.getRuc() != null ? cliente.getRuc() : "");
        txtOtroDocumento.setText(cliente.getOtroDocumento() != null ? cliente.getOtroDocumento() : "");
        
        txtDireccion.setText(cliente.getDireccion() != null ? cliente.getDireccion() : "");
        txtTelefono.setText(cliente.getTelefono() != null ? cliente.getTelefono() : "");
        txtEmail.setText(cliente.getEmail() != null ? cliente.getEmail() : "");
        
        txtMetadata.setText(cliente.getMetadata() != null ? cliente.getMetadata() : "");
        
        chkTieneCredito.setSelected(cliente.getTieneCredito() != null ? cliente.getTieneCredito() : false);
        chkEsMayorista.setSelected(cliente.getEsMayorista() != null ? cliente.getEsMayorista() : false);
        chkEstado.setSelected(cliente.getEstado() != null ? cliente.getEstado() : true);
    }

    private void showError(String message) {
        // Usar alertUtil si está disponible, sino usar Alert básico
        if (alertUtil != null) {
            alertUtil.showError(message);
        } else {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Error");
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        }
    }
}
