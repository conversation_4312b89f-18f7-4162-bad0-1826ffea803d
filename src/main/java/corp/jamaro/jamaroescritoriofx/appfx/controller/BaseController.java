package corp.jamaro.jamaroescritoriofx.appfx.controller;

import javafx.application.Platform;
import javafx.fxml.Initializable;
import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.core.Disposables;

import java.util.function.Consumer;

/**
 * Controlador base que centraliza la lógica reactiva y de UI.
 */
@Slf4j
public abstract class BaseController implements Initializable {

    private final Disposable.Composite disposables = Disposables.composite();

    /**
     * Registra una suscripción para poder limpiarla luego.
     */
    protected void registerSubscription(Disposable disposable) {
        disposables.add(disposable);
    }

    /**
     * Cancela todas las suscripciones para prevenir fugas de memoria.
     * Debe llamarse cuando la vista ya no se use.
     */
    public void onClose() {
        disposables.dispose();
        log.debug("Disposables liberados para {}", getClass().getSimpleName());
    }

    /**
     * Ejecuta una tarea en el hilo de la UI.
     */
    protected void runOnUiThread(Runnable task) {
        Platform.runLater(task);
    }

    /**
     * Helper para suscribirse a un Mono en el scheduler boundedElastic.
     */
    protected <T> void subscribeOnBoundedElastic(
            Mono<T> mono, 
            Consumer<T> onSuccess, 
            Consumer<Throwable> onError
    ) {
        Disposable disposable = mono
            .subscribeOn(Schedulers.boundedElastic())
            .subscribe(onSuccess, onError);
        registerSubscription(disposable);
    }

    /**
     * Helper para loguear errores de forma consistente.
     */
    protected Consumer<Throwable> logError(String context) {
        return error -> log.error("Error {}: {}", context, error.getMessage(), error);
    }
}