package corp.jamaro.jamaroescritoriofx.appfx.dinero.model;

import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class CobroDineroProgramado {

    private UUID id;

    private Double montoACobrar;//el monto en Soles ya que el sistema trabajar por defecto en soles.
    private Double montoRestante;//el monto restante a cobrar, se actualiza cada vez que se cobra dinero.
    private Set<Dinero> dineroCobrados ;//pueden ser varios ya que hay varios metodos de pago y puede ser pago mixto,en un inicio nisiquiera va a existir hasta que se cobre, se programará un cobro y cuando se cobra recien se crea el dinero con esEntrada true.

    private String iniciadoPor;//username que programo el cobro

    private Instant creadoEl;
    private Instant fechaLimite;

    private Instant terminadoDeCobrarEl;

    private Boolean estaCobrado;// true (cobrado por completo) false (no cobrado por completo) para encontrar facilmente que dinero faltan cobrar en caja.
}
