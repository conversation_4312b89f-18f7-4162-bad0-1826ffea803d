package corp.jamaro.jamaroescritoriofx.appfx.dinero.model;

import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class DevolucionDinero {
    private UUID id;

    private Double montoADevolver;//el monto en Soles ya que el sistema trabajar por defecto en soles a devolver

    private Set<Dinero> dineroDevuelto;//es un set ya que puede ser una devolucion mixta yape y efectivo por ejemplo; en un inicio nisiquiera va a existir hasta que se haga la devolucion.

    private String iniciadoPor;//username que programo la devolucion
    private String devueltoPor;//username que devolvio el dinero

    private Instant creadoEl;
    private Instant devueltoEl;

    private Boolean estaDevuelto;// true (devuelto) false (no devuelto) para encontrar facilmente que dinero faltan devolver en caja.

}
