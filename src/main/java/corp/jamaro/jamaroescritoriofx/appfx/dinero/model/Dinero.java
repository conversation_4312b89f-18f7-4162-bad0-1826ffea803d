package corp.jamaro.jamaroescritoriofx.appfx.dinero.model;

import corp.jamaro.jamaroescritoriofx.appfx.dinero.model.enums.TipoMoneda;
import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class Dinero {
    private UUID id;

    private String tramitadoPor;//User que da o recibe el dinero

    private TipoMoneda tipoDeMoneda = TipoMoneda.SOLES;//por defecto SOLES pero puede ser otras
    private Double montoAntesDelCambio; // es el monto recibido o emitido en el tipo de moneda antes del cambio
    private Double factorDeCambio; //Por defecto 1.00 ya que la moneda por defecto es el sol pero si fuera dolares ejemplo 3.41

    private Double montoReal;//es el monto real en moneda base (soles) (montoAntesDelCambio * factorDeCambio) con este dato hace calculos el sistema

    private TipoDeDinero tipoDeDinero;

    private String detalles; // no siempre es necesario detalles de tipo de pago por ejemplo si es digital, yape, transferencia, etc, si es efectivo series de moneda entre otros

    private Instant createdAt=Instant.now();

    //recuerda que los cuadres de caja se hacen con entradas menos salidas de dinero
    private Boolean esEntrada;// true (entrada) false (salida)

    public enum TipoDeDinero {
        EFECTIVO,
        DIGITAL
    }
}
