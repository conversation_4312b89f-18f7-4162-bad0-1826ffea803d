package corp.jamaro.jamaroescritoriofx.appfx.model.collaborative;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Data
public class CollaborativeRoom {
    private UUID id;

    private String iniciadaPor;//username del user que inició el room

    private Set<UserConnected> usersConnected = new HashSet<>();

    private List<ChatMessage> chatMessages = new ArrayList<>();

}
