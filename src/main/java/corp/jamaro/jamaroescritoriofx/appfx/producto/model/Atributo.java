package corp.jamaro.jamaroescritoriofx.appfx.producto.model;

import corp.jamaro.jamaroescritoriofx.appfx.model.file.ToBucketFileRelation;
import lombok.Data;

import java.util.Set;
import java.util.UUID;

@Data
public class Atributo {
    private UUID id;

    private Filtro filtro;

    private String datoString;
    private Double datoNumerico;

    private String datoDicotomico;//solo puede tomar dos valores y la incertidumbre que sería null.
    //datos dicotomicos ejemplo con-abs, sin-abs; delantero, posterior; izquierdo, derecho; imantado, no-imantado.

    private String datoCompuesto;//ejemplo para mangueras de alternador y freno terminal|diametro|conico = macho|10|cono-da

//    private Set<ToBucketFileRelation> datoFile;// Generalmente van a ser Imagenes, pero pueden ser animaciones, modelos 3d etc.
}
