package corp.jamaro.jamaroescritoriofx.appfx.producto.model;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoCategoria;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class Grupo {
    private String id;//por ahora va ser la abreviación de la Categoria de la db antigua

    private String nombrePrincipal;

    private TipoCategoria tipo; //categoria, sub categoria, familia, etc

    private Set<NombreGrupo> nombresGrupo; //asegurarnos de que cada NombreCategoria le pertenezca solo a una Categoria

    private Set<Grupo> subGrupos;

    private List<GrupoFiltroRelation> filtros;//aquí es list xq se puede repetir un filtro



}
