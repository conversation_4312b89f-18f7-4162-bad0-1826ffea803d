package corp.jamaro.jamaroescritoriofx.appfx.producto.model;

import corp.jamaro.jamaroescritoriofx.appfx.model.file.ToBucketFileRelation;
import lombok.Data;

import java.util.Set;
import java.util.UUID;

@Data
public class Item {
    private UUID id;

    private String codCompuesto;//código interno que se forma con ciertos atributos de la lógica de negocio

    private String descripcion;

    private Double precioCostoPromedio;//un promedio del precio costo según los precio de compra hechos a diferentes proveedores.
    private Double precioVentaBase;//precio minimo de venta.
    private Double precioVentaPublico;//precio de venta que aparecerá en el sistema

    private Double stockTotal;
    private Double stockDeSeguridad;

    private Set<Producto> productos;

    private Set<CodigoFabrica> codigosFabrica;

    private Marca marca;

    private Set<Atributo> atributos;

    private Set<Ubicacion> ubicaciones;

    private Set<ToBucketFileRelation> imagenes;

    // borrar este luego
    private String anotacionesOld;
    private String codProductoOld;

    //implementar luego su respectivo nodo
    private String vehiculoOld;
    private String codFabricaOld;


}
