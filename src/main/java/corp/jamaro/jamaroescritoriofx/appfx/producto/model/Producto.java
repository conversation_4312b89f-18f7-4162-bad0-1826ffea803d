package corp.jamaro.jamaroescritoriofx.appfx.producto.model;

import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.Vehiculo;
import lombok.Data;

import java.util.Set;
import java.util.UUID;

@Data
public class Producto {
    private UUID id;

    private String codProductoOld;
    private String descripcion;

    private Set<Atributo> atributos;

    private Set<Vehiculo> vehiculos;

    private Set<Grupo> grupos;

    private Set<CodigoFabrica> codigosFabrica;


}