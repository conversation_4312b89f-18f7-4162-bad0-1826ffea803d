package corp.jamaro.jamaroescritoriofx.appfx.producto.model;

import corp.jamaro.jamaroescritoriofx.appfx.model.file.ToBucketFileRelation;
import lombok.Data;

import java.util.Set;
import java.util.UUID;

@Data
public class Ubicacion {
    private UUID id;
    private String nombre;// sera codificado en base a estante piso separación
    private Set<ToBucketFileRelation> imagenes;
    private Boolean isPrincipal = true;// para saber si es la fuente principal de donde se descontará primero el stock
}
