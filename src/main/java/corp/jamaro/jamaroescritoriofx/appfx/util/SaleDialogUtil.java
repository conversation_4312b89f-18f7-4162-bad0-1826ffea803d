package corp.jamaro.jamaroescritoriofx.appfx.util;

import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.Sale;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.stage.StageStyle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.NumberFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Utilidad para crear y mostrar diálogos específicos relacionados con ventas.
 * Centraliza la lógica de diálogos complejos para mantener los controladores más limpios.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SaleDialogUtil {

    private static final String STYLESHEET_PATH = "/css/styles.css";
    private static final String SEARCH_PRODUCT_STYLESHEET_PATH = "/css/searchProduct.css";

    private final AlertUtil alertUtil;
    private NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();

    /**
     * Muestra un mensaje de éxito específico según el tipo de venta procesada.
     */
    public void showVentaProcesamientoExitoso(Sale.TipoVenta tipoVenta, Double totalMontoAcordado) {
        String tipoVentaText = getTipoVentaDisplayText(tipoVenta);
        String montoText = totalMontoAcordado != null ? currencyFormat.format(totalMontoAcordado) : "N/A";
        
        String title = "Venta Procesada";
        String message = String.format(
                """
                        ✅ %s procesada exitosamente
                        
                        💰 Monto total: %s
                        📋 Estado: %s""",
            tipoVentaText,
            montoText,
            getEstadoVentaText(tipoVenta)
        );

        alertUtil.showInfo(title, message);
        log.info("Venta procesada exitosamente - Tipo: {}, Monto: {}", tipoVenta, montoText);
    }

    /**
     * Muestra un diálogo para seleccionar el tipo de venta.
     */
    public Optional<Sale.TipoVenta> showTipoVentaSelectionDialog(Sale currentSale) {
        Dialog<Sale.TipoVenta> dialog = new Dialog<>();
        dialog.setTitle("Tipo de Venta");

        // Crear botones
        ButtonType aceptarButtonType = new ButtonType("Aceptar", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelarButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(aceptarButtonType, cancelarButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(12);
        contentBox.setPadding(new Insets(15));

        // Título
        Label titleLabel = new Label("Seleccione el tipo de venta:");
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 13px; -fx-font-weight: 600;");

        // ComboBox con opciones
        ComboBox<Sale.TipoVenta> comboBox = new ComboBox<>();

        // Filtrar opciones: agregar solo PROFORMA, CREDITO y PEDIDO (excluir CONTADO)
        List<Sale.TipoVenta> allowedTypes = Arrays.asList(
                Sale.TipoVenta.PROFORMA,
                Sale.TipoVenta.CREDITO,
                Sale.TipoVenta.PEDIDO
        );
        comboBox.getItems().addAll(allowedTypes);

        // Seleccionar el tipo actual si está en la lista permitida
        if (currentSale != null && currentSale.getTipoVenta() != null &&
            allowedTypes.contains(currentSale.getTipoVenta())) {
            comboBox.setValue(currentSale.getTipoVenta());
        } else {
            comboBox.setValue(Sale.TipoVenta.PROFORMA);
        }

        // Configurar ComboBox para mejor visibilidad
        comboBox.setPrefWidth(200);
        comboBox.setMaxWidth(Double.MAX_VALUE);

        contentBox.getChildren().addAll(titleLabel, comboBox);
        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-file-invoice");

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == aceptarButtonType) {
                return comboBox.getValue();
            }
            return null;
        });

        return dialog.showAndWait();
    }

    /**
     * Muestra un diálogo para seleccionar el tipo de venta al momento de procesar la venta.
     * Este diálogo es específico para el proceso de venta y no requiere un Sale existente.
     */
    public Optional<Sale.TipoVenta> showTipoVentaSelectionDialogForSale() {
        Dialog<Sale.TipoVenta> dialog = new Dialog<>();
        dialog.setTitle("Procesar Venta");

        // Crear botones
        ButtonType procesarButtonType = new ButtonType("Procesar Venta", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelarButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        dialog.getDialogPane().getButtonTypes().addAll(procesarButtonType, cancelarButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(15);
        contentBox.setPadding(new Insets(20));
        contentBox.setAlignment(Pos.CENTER);

        // Título
        Label titleLabel = new Label("Seleccione el tipo de venta:");
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 14px; -fx-font-weight: 600;");

        // ComboBox con opciones
        ComboBox<Sale.TipoVenta> comboBox = new ComboBox<>();

        // Agregar todas las opciones disponibles para procesar venta
        List<Sale.TipoVenta> availableTypes = Arrays.asList(
                Sale.TipoVenta.PROFORMA,  // Para venta al contado
                Sale.TipoVenta.CREDITO,
                Sale.TipoVenta.PEDIDO
        );

        comboBox.getItems().addAll(availableTypes);
        comboBox.setValue(Sale.TipoVenta.PROFORMA); // Valor por defecto

        // Configurar el cell factory para mostrar texto personalizado
        comboBox.setCellFactory(listView -> new ListCell<>() {
            @Override
            protected void updateItem(Sale.TipoVenta item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(getTipoVentaDisplayText(item));
                }
            }
        });

        // Configurar el button cell para mostrar el texto seleccionado
        comboBox.setButtonCell(new ListCell<>() {
            @Override
            protected void updateItem(Sale.TipoVenta item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || item == null) {
                    setText(null);
                } else {
                    setText(getTipoVentaDisplayText(item));
                }
            }
        });

        // Configurar ComboBox para mejor visibilidad
        comboBox.setPrefWidth(250);
        comboBox.setMaxWidth(Double.MAX_VALUE);

        // Descripción del tipo seleccionado
        Label descriptionLabel = new Label();
        descriptionLabel.setStyle("-fx-text-fill: #a0a0a0; -fx-font-size: 12px;");
        descriptionLabel.setWrapText(true);
        descriptionLabel.setMaxWidth(250);

        // Actualizar descripción cuando cambie la selección
        comboBox.valueProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                descriptionLabel.setText(getEstadoVentaText(newVal));
            }
        });

        // Establecer descripción inicial
        descriptionLabel.setText(getEstadoVentaText(Sale.TipoVenta.PROFORMA));

        contentBox.getChildren().addAll(titleLabel, comboBox, descriptionLabel);
        dialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(dialog);
        setDialogIcon(dialog, "fas-cash-register");

        // Configurar el resultado del diálogo
        dialog.setResultConverter(dialogButton -> {
            if (dialogButton == procesarButtonType) {
                return comboBox.getValue();
            }
            return null;
        });

        // Configurar focus en el ComboBox
        Platform.runLater(comboBox::requestFocus);

        return dialog.showAndWait();
    }

    /**
     * Muestra un diálogo para ingresar la cantidad de un item.
     */
    public Optional<String> showCantidadDialog(String itemCode, String itemDescription) {
        Dialog<String> quantityDialog = new Dialog<>();
        quantityDialog.setTitle("Agregar Item");
        quantityDialog.setHeaderText(null);

        // Configurar botones
        ButtonType aceptarButtonType = new ButtonType("Aceptar", ButtonBar.ButtonData.OK_DONE);
        ButtonType cancelarButtonType = new ButtonType("Cancelar", ButtonBar.ButtonData.CANCEL_CLOSE);
        quantityDialog.getDialogPane().getButtonTypes().addAll(aceptarButtonType, cancelarButtonType);

        // Crear contenido organizado con VBox
        VBox contentBox = new VBox(12);
        contentBox.setPadding(new Insets(15));

        // Título
        Label titleLabel = new Label("Ingrese la cantidad para:");
        titleLabel.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 13px; -fx-font-weight: 600;");

        // Información del producto con colores diferenciados
        VBox productBox = new VBox(4);

        if (itemCode != null && !itemCode.isEmpty()) {
            javafx.scene.layout.HBox codeBox = new javafx.scene.layout.HBox(8);
            Label codeLabel = new Label("🏷️ Código:");
            codeLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-size: 11px; -fx-font-weight: bold; -fx-min-width: 80px;");
            Label codeValue = new Label(itemCode);
            codeValue.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 11px;");
            codeBox.getChildren().addAll(codeLabel, codeValue);
            productBox.getChildren().add(codeBox);
        }

        if (itemDescription != null && !itemDescription.isEmpty()) {
            javafx.scene.layout.HBox descBox = new javafx.scene.layout.HBox(8);
            Label descLabel = new Label("📝 Descripción:");
            descLabel.setStyle("-fx-text-fill: #3ca0aa; -fx-font-size: 11px; -fx-font-weight: bold; -fx-min-width: 80px;");
            Label descValue = new Label(itemDescription);
            descValue.setStyle("-fx-text-fill: #dbdce1; -fx-font-size: 11px;");
            descValue.setWrapText(true);
            descValue.setMaxWidth(200);
            descBox.getChildren().addAll(descLabel, descValue);
            productBox.getChildren().add(descBox);
        }

        // TextField para la cantidad
        TextField textField = new TextField("1.0");
        textField.setPromptText("Ej: 1.0, 2.5, 10");
        textField.setPrefWidth(120);
        textField.setMaxWidth(Double.MAX_VALUE);

        contentBox.getChildren().addAll(titleLabel, productBox, textField);
        quantityDialog.getDialogPane().setContent(contentBox);

        // Aplicar estilos modernos
        applyDialogStyles(quantityDialog);
        setDialogIcon(quantityDialog, "fas-plus-circle");

        // Configurar el resultado del diálogo
        quantityDialog.setResultConverter(dialogButton -> {
            if (dialogButton == aceptarButtonType) {
                return textField.getText();
            }
            return null;
        });

        // Configurar focus y selección
        Platform.runLater(() -> {
            textField.requestFocus();
            textField.selectAll();
        });

        return quantityDialog.showAndWait();
    }

    /**
     * Obtiene el texto de visualización para el tipo de venta.
     */
    private String getTipoVentaDisplayText(Sale.TipoVenta tipoVenta) {
        return switch (tipoVenta) {
            case PROFORMA -> "Venta al Contado";
            case CREDITO -> "Venta a Crédito";
            case PEDIDO -> "Pedido";
            case CONTADO -> "Venta al Contado";
        };
    }

    /**
     * Obtiene el texto del estado según el tipo de venta.
     */
    private String getEstadoVentaText(Sale.TipoVenta tipoVenta) {
        return switch (tipoVenta) {
            case PROFORMA -> "Pendiente de pago (30 min)";
            case CREDITO -> "Pendiente de pago a crédito";
            case PEDIDO -> "Pendiente de entrega";
            case CONTADO -> "Pendiente de pago";
        };
    }

    /**
     * Aplica estilos modernos a un diálogo.
     */
    private void applyDialogStyles(Dialog<?> dialog) {
        // Hacer el diálogo sin decoraciones para un diseño moderno
        dialog.initStyle(StageStyle.UNDECORATED);

        // Aplicar estilos CSS
        dialog.getDialogPane().getStylesheets().addAll(
                getClass().getResource(STYLESHEET_PATH).toExternalForm(),
                getClass().getResource(SEARCH_PRODUCT_STYLESHEET_PATH).toExternalForm()
        );

        // Hacer el diálogo arrastrable
        makeDraggable(dialog);
    }

    /**
     * Configura un icono para el diálogo.
     */
    private void setDialogIcon(Dialog<?> dialog, String iconClass) {
        // Implementación básica - se puede expandir con iconos reales
        log.debug("Configurando icono {} para diálogo", iconClass);
    }

    /**
     * Hace que el diálogo sea arrastrable.
     */
    private void makeDraggable(Dialog<?> dialog) {
        final double[] xOffset = {0};
        final double[] yOffset = {0};

        dialog.getDialogPane().setOnMousePressed(event -> {
            xOffset[0] = event.getSceneX();
            yOffset[0] = event.getSceneY();
        });

        dialog.getDialogPane().setOnMouseDragged(event -> {
            dialog.setX(event.getScreenX() - xOffset[0]);
            dialog.setY(event.getScreenY() - yOffset[0]);
        });
    }
}
