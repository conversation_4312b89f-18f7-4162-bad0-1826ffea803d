package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoNombre;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.VehiculoService;
import javafx.application.Platform;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.stage.Stage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.CustomTextField;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.ResourceBundle;
import java.util.UUID;

/**
 * Controlador para agregar (o editar) un VehiculoNombre localmente,
 * con búsqueda en el servidor para evitar duplicados y mostrar coincidencias.
 *
 * - En modo Creación: se crea localmente el VehiculoNombre (sin llamar al servidor).
 * - En modo Edición: se actualiza el VehiculoNombre en el servidor con saveVehiculoNombre(...).
 */
@Slf4j
@Component
@Scope("prototype")
@RequiredArgsConstructor
public class AgregarVehiculoNombre extends BaseController {

    // ------------------------------------------------------------------------
    // FXML
    // ------------------------------------------------------------------------
    @FXML
    private Button btnAgregar;

    @FXML
    private Button btnLimpiar;

    @FXML
    private Button btnSalir;

    @FXML
    private ListView<VehiculoNombre> lvCoincidencias;

    @FXML
    private CustomTextField txtVehiculoNombre;

    // ------------------------------------------------------------------------
    // Dependencias
    // ------------------------------------------------------------------------
    private final AlertUtil alertUtil;
    private final VehiculoService vehiculoService;

    // ------------------------------------------------------------------------
    // Estado interno
    // ------------------------------------------------------------------------
    /** Si es null => modo creación, si no => modo edición. */
    private VehiculoNombre nombreEnEdicion;

    /** Label para mostrar la marca a la izquierda del CustomTextField. */
    private Label lblMarca;

    /** Guardamos la marca recibida para luego concatenarla (ej: "Toyota"). */
    private String marcaEnLeft;

    // ------------------------------------------------------------------------
    // Initialize
    // ------------------------------------------------------------------------
    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // Inicia en modo creación si no se llama initVehiculoNombre(...)
        setModoCreacion();

        // Creamos el Label que mostraremos a la izquierda del txtVehiculoNombre
        lblMarca = new Label();
        txtVehiculoNombre.setLeft(lblMarca);

        configurarListView();
        configurarListenerTexto();
    }

    // ------------------------------------------------------------------------
    // METODO PRINCIPAL DE INICIALIZACIÓN
    // ------------------------------------------------------------------------
    /**
     * Inicializa el formulario con un VehiculoNombre existente y una marca.
     * - Si vehiculoNombre es null => modo creación.
     * - De lo contrario => modo edición.
     */
    public void initVehiculoNombre(VehiculoNombre vehiculoNombre, String marca) {
        this.marcaEnLeft = (marca == null) ? "" : marca.trim();
        lblMarca.setText(this.marcaEnLeft + " ");

        if (vehiculoNombre == null) {
            setModoCreacion();
        } else {
            setModoEdicion(vehiculoNombre);
        }
    }

    /**
     * Retorna el VehiculoNombre creado o editado (puede ser null si no se guardó).
     */
    public VehiculoNombre getVehiculoNombreCreado() {
        return nombreEnEdicion;
    }

    // ------------------------------------------------------------------------
    // Configuración del ListView
    // ------------------------------------------------------------------------
    private void configurarListView() {
        // Doble clic => cargar ese nombre en edición
        lvCoincidencias.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2) {
                VehiculoNombre seleccionado = lvCoincidencias.getSelectionModel().getSelectedItem();
                if (seleccionado != null) {
                    setModoEdicion(seleccionado);
                }
            }
        });
    }

    // ------------------------------------------------------------------------
    // Listener en el texto => búsqueda dinámica
    // ------------------------------------------------------------------------
    private void configurarListenerTexto() {
        txtVehiculoNombre.textProperty().addListener((obs, oldVal, newVal) -> {
            // Armar el nombre final => marca + lo que escribe el usuario
            String finalName = (marcaEnLeft + " " + newVal).trim();

            // Si está vacío => limpiamos la lista
            if (finalName.isBlank()) {
                lvCoincidencias.getItems().clear();
            } else {
                // Buscar en el servidor
                searchCoincidencias(finalName);
            }
        });
    }

    /**
     * Llama al servicio searchVehiculoNombre y muestra los resultados.
     */
    private void searchCoincidencias(String finalName) {
        Disposable sub = vehiculoService.searchVehiculoNombre(finalName)
                .collectList()
                .subscribe(
                        list -> Platform.runLater(() -> {
                            lvCoincidencias.getItems().setAll(list);
                        }),
                        error -> Platform.runLater(() -> {
                            log.error("Error al buscar coincidencias: {}", error.getMessage(), error);
                            alertUtil.showError(error);
                        })
                );
        registerSubscription(sub);
    }

    // ------------------------------------------------------------------------
    // BOTONES
    // ------------------------------------------------------------------------
    @FXML
    void onBtnAgregarClick(ActionEvent event) {
        String textoIngresado = txtVehiculoNombre.getText().trim();
        if (textoIngresado.isEmpty()) {
            alertUtil.showError("Debe ingresar un nombre antes de guardar.");
            return;
        }

        // Construir el nombre final => p.e. "Toyota Probox"
        String finalName = (marcaEnLeft + " " + textoIngresado).trim();

        // Verificar duplicados en lvCoincidencias
        boolean yaExiste = lvCoincidencias.getItems().stream()
                .anyMatch(vn -> vn.getNombre() != null
                        && vn.getNombre().equalsIgnoreCase(finalName));

        if (yaExiste) {
            alertUtil.showError("Ya existe un nombre igual a \"" + finalName + "\". No se puede duplicar.");
            return;
        }

        boolean esCreacion = (nombreEnEdicion == null);
        if (esCreacion) {
            // --- MODO CREACIÓN ---
            // => Solo creamos el objeto local, sin llamar al servidor
            nombreEnEdicion = new VehiculoNombre();
            nombreEnEdicion.setId(UUID.randomUUID());
            nombreEnEdicion.setNombre(finalName);

            log.info("VehiculoNombre creado localmente: {}", nombreEnEdicion);
            // Cerramos la ventana y el principal se encargará de persistirlo si lo desea
            cerrarVentana();

        } else {
            // --- MODO EDICIÓN ---
            // => Actualizamos y llamamos al servidor para guardar cambios
            nombreEnEdicion.setNombre(finalName);

            Disposable sub = vehiculoService.saveVehiculoNombre(nombreEnEdicion)
                    .subscribe(
                            updatedNombre -> Platform.runLater(() -> {
                                log.info("VehiculoNombre editado con éxito: {}", updatedNombre);
                                alertUtil.showInfo(
                                        "Nombre de vehículo actualizado",
                                        "Se ha guardado correctamente:\n" + updatedNombre.getNombre()
                                );
                                // Actualizar la variable local con la versión actualizada del servidor
                                nombreEnEdicion = updatedNombre;
                                cerrarVentana();
                            }),
                            error -> Platform.runLater(() -> {
                                log.error("Error al actualizar VehiculoNombre: {}", error.getMessage(), error);
                                alertUtil.showError(error);
                            })
                    );
            registerSubscription(sub);
        }
    }

    @FXML
    void onBtnLimpiarClick(ActionEvent event) {
        setModoCreacion();
        // Limpia la lista
        lvCoincidencias.getItems().clear();
    }

    @FXML
    void onBtnSalirClick(ActionEvent event) {
        cerrarVentana();
    }

    // ------------------------------------------------------------------------
    // Modo Creación / Edición
    // ------------------------------------------------------------------------
    private void setModoCreacion() {
        nombreEnEdicion = null;
        txtVehiculoNombre.clear();
        btnAgregar.setText("Crear");

        Platform.runLater(() -> {
            setWindowTitle("Creando Nombre de Vehículo");
            // Mostrar coincidencias al iniciar, si la marca no está vacía
            if (marcaEnLeft != null && !marcaEnLeft.isBlank()) {
                searchCoincidencias(marcaEnLeft);
            }
        });
    }

    /**
     * Modo edición:
     * 1. Ajusta el texto del txtVehiculoNombre quitando el prefijo de la marca (si lo tiene).
     * 2. Llama a searchCoincidencias(...) con el 'fullName' completo,
     *    para que el ListView muestre ese nombre.
     */
    private void setModoEdicion(VehiculoNombre vehiculoNombre) {
        nombreEnEdicion = vehiculoNombre;
        btnAgregar.setText("Editar");

        String fullName = (vehiculoNombre.getNombre() == null) ? "" : vehiculoNombre.getNombre();
        String prefix = (marcaEnLeft == null) ? "" : marcaEnLeft.trim() + " ";

        // Ej: si "Toyota Probox" startsWith "Toyota", recortamos esa parte
        String parteEditable = fullName;
        if (!prefix.isBlank() && fullName.startsWith(prefix)) {
            parteEditable = fullName.substring(prefix.length());
        }
        txtVehiculoNombre.setText(parteEditable);

        // Así buscamos la "coincidencia completa" => "Toyota Probox"
        if (!fullName.isBlank()) {
            searchCoincidencias(fullName);
        }

        Platform.runLater(() -> setWindowTitle("Editando Nombre de Vehículo"));
    }

    // ------------------------------------------------------------------------
    // Utilidades
    // ------------------------------------------------------------------------
    private void setWindowTitle(String newTitle) {
        Stage stage = (Stage) btnAgregar.getScene().getWindow();
        stage.setTitle(newTitle);
    }

    private void cerrarVentana() {
        Stage stage = (Stage) btnSalir.getScene().getWindow();
        stage.close();
    }
}
