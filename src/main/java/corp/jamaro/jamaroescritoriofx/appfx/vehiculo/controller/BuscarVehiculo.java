package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoNombre;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.VehiculoService;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.stage.Stage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.CustomTextField;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

/**
 * Controlador para buscar un Vehículo (o más bien un VehiculoNombre),
 * y luego notificar al controlador padre (CreacionVehiculoController)
 * para que cargue la info completa de ese Vehiculo.
 */
@Slf4j
@Component
@Scope("prototype")
@RequiredArgsConstructor
public class BuscarVehiculo extends BaseController {

    // ------------------------------------------------------------------------
    // FXML Fields
    // ------------------------------------------------------------------------
    @FXML
    private Button btnEditar;

    @FXML
    private Button btnLimpiar;

    @FXML
    private Button btnSalir;

    @FXML
    private CustomTextField txtNombreVehiculo;

    /**
     * ListView para mostrar los resultados de VehiculoNombre
     * en lugar de un TableView.
     */
    @FXML
    private ListView<VehiculoNombre> lvResultados;

    // ------------------------------------------------------------------------
    // Dependencias
    // ------------------------------------------------------------------------
    private final VehiculoService vehiculoService;
    private final AlertUtil alertUtil;

    // ------------------------------------------------------------------------
    // Otros campos internos
    // ------------------------------------------------------------------------
    private CreacionVehiculoController parentController;

    /**
     * Lista completa de VehiculoNombre que se obtiene al inicio (getAllVehiculoNombres).
     * Luego filtramos sobre ella según textoIngresado en txtNombreVehiculo.
     */
    private List<VehiculoNombre> allNombres;

    /**
     * Lista observable para la ListView (y la mantenemos filtrada).
     */
    private final ObservableList<VehiculoNombre> filteredNombres = FXCollections.observableArrayList();

    // ------------------------------------------------------------------------
    // Ciclo de vida del Controller
    // ------------------------------------------------------------------------
    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        configurarListView();
        configurarEventos();
        cargarTodosLosNombres();
    }

    /**
     * Recibe el controlador padre para invocar initVehiculo(vehiculo) más adelante.
     */
    public void setParentController(CreacionVehiculoController creacionVehiculoController) {
        this.parentController = creacionVehiculoController;
    }

    // ------------------------------------------------------------------------
    // Configuración de la ListView y eventos
    // ------------------------------------------------------------------------
    private void configurarListView() {
        // Asignamos la lista observable
        lvResultados.setItems(filteredNombres);

        // Configurar el CellFactory para mostrar el .toString() de cada VehiculoNombre
        lvResultados.setCellFactory(listView -> {
            ListCell<VehiculoNombre> cell = new ListCell<>() {

                {
                    // Detectar doble clic para editar (similar a la rowFactory en TableView)
                    setOnMouseClicked(event -> {
                        if (event.getClickCount() == 2 && !isEmpty()) {
                            editarVehiculoDesdeNombre(getItem());
                        }
                    });
                }

                @Override
                protected void updateItem(VehiculoNombre item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty || item == null) {
                        setText(null);
                    } else {
                        // Usamos toString() -> en VehiculoNombre retorna el nombre
                        setText(item.toString());
                    }
                }
            };
            return cell;
        });

        // Deshabilitar btnEditar si no hay selección
        btnEditar.disableProperty().bind(
                lvResultados.getSelectionModel().selectedItemProperty().isNull()
        );
    }

    /**
     * Configura listener en txtNombreVehiculo para filtrar "en vivo".
     */
    private void configurarEventos() {
        // Cuando cambie el texto => filtrar
        txtNombreVehiculo.textProperty().addListener((obs, oldVal, newVal) -> {
            filtrarNombres(newVal);
        });
    }

    // ------------------------------------------------------------------------
    // Cargar datos
    // ------------------------------------------------------------------------
    private void cargarTodosLosNombres() {
        Disposable sub = vehiculoService.getAllVehiculoNombres()
                .collectList()
                .subscribe(
                        lista -> Platform.runLater(() -> {
                            this.allNombres = lista;
                            // Al inicio, si el usuario no ha escrito nada => mostrar todos
                            filteredNombres.setAll(lista);
                        }),
                        error -> Platform.runLater(() -> {
                            log.error("Error al cargar VehiculoNombre: {}", error.getMessage());
                            alertUtil.showError(error);
                        })
                );
        registerSubscription(sub);
    }

    /**
     * Filtra la lista 'allNombres' según el texto ingresado por el usuario.
     * Si el texto está vacío => mostrar todo.
     */
    private void filtrarNombres(String texto) {
        if (allNombres == null || allNombres.isEmpty()) {
            return;
        }
        if (texto == null || texto.isBlank()) {
            // Si no hay nada ingresado, mostrar todo
            filteredNombres.setAll(allNombres);
        } else {
            String lowerText = texto.toLowerCase();
            // Filtramos sin comodines, solo contains
            List<VehiculoNombre> filtrados = allNombres.stream()
                    .filter(vn -> vn.getNombre() != null
                            && vn.getNombre().toLowerCase().contains(lowerText))
                    .collect(Collectors.toList());
            filteredNombres.setAll(filtrados);
        }
    }

    // ------------------------------------------------------------------------
    // Eventos de Botones
    // ------------------------------------------------------------------------
    @FXML
    void onBtnEditarClick(ActionEvent event) {
        // Solo si hay un elemento seleccionado en la ListView
        var selected = lvResultados.getSelectionModel().getSelectedItem();
        if (selected != null) {
            editarVehiculoDesdeNombre(selected);
        }
    }

    /**
     * Limpia el txtNombreVehiculo y reinicia la ListView (allNombres).
     */
    @FXML
    void onBtnLimpiarClick(ActionEvent event) {
        txtNombreVehiculo.clear();
    }

    /**
     * Cierra esta ventana y regresa al principal.
     */
    @FXML
    void onBtnSalirClick(ActionEvent event) {
        Stage stage = (Stage) btnSalir.getScene().getWindow();
        stage.close();
    }

    // ------------------------------------------------------------------------
    // Lógica para editar => obtener Vehiculo y enviar al controlador principal
    // ------------------------------------------------------------------------
    private void editarVehiculoDesdeNombre(VehiculoNombre vehiculoNombre) {
        if (vehiculoNombre == null) return;

        // 1) Llamar a service.getVehiculoByVehiculoNombreId(...)
        Disposable sub = vehiculoService.getVehiculoByVehiculoNombreId(vehiculoNombre.getId())
                .subscribe(
                        vehiculo -> Platform.runLater(() -> {
                            if (vehiculo != null) {
                                // 2) Pasar el Vehiculo al parentController
                                parentController.initVehiculo(vehiculo);

                                // 3) Cerrar esta ventana
                                Stage stage = (Stage) lvResultados.getScene().getWindow();
                                stage.close();
                            } else {
                                log.warn("No se encontró un Vehiculo asociado a {}", vehiculoNombre);
                                alertUtil.showWarning("No se encontró un vehículo asociado a ese nombre.");
                            }
                        }),
                        error -> Platform.runLater(() -> {
                            log.error("Error al obtener Vehiculo: {}", error.getMessage());
                            alertUtil.showError(error);
                        })
                );
        registerSubscription(sub);
    }
}
