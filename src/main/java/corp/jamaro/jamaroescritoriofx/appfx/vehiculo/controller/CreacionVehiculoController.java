package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.*;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.VehiculoService;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.creacionvehiculo.VehiculoBaseDataService;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.creacionvehiculo.VehiculoImageManager;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.creacionvehiculo.VehiculoNamesManager;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.creacionvehiculo.VehiculoValidationService;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.viewmodel.VehiculoViewModel;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.collections.ListChangeListener;
import javafx.fxml.FXML;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.TextFormatter;
import javafx.scene.image.ImageView;
import javafx.scene.input.*;
import javafx.stage.Modality;
import javafx.stage.Stage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.SearchableComboBox;
import org.controlsfx.control.textfield.CustomTextField;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.UnaryOperator;

/**
 * Controlador para la creación/edición de Vehículos.
 *
 * - Usa un ViewModel (VehiculoViewModel) para simplificar el binding.
 * - Soporta creación y edición de un Vehiculo, junto a la edición de nombres
 *   adicionales e imágenes asociadas.
 * - Permite abrir ventanas hijas (Marca, Modelo, Motor, Versión) y luego
 *   refresca los combos para no perder la selección previa ni machacar datos.
 *
 * Este controlador delega gran parte de la lógica a servicios especializados:
 * - VehiculoBaseDataService: Carga de datos "base" y combos.
 * - VehiculoValidationService: Validaciones de campos (años).
 * - VehiculoImageManager: Manejo de imágenes y del índice actual.
 * - VehiculoNamesManager: Manejo de nombres adicionales (agregar, eliminar, reordenar).
 *
 * REGLA ADICIONAL:
 * - btnAddNombreVehiculo, btnCrearVehiculo y btnAddImage sólo estarán activos
 *   cuando haya Marca, Modelo y Motor seleccionados.
 */
@Slf4j
@Component
@Scope("prototype")
@RequiredArgsConstructor
public class CreacionVehiculoController extends BaseController {

    // ------------------------------------------------------------------------
    // Constantes para evitar "magic strings"
    // ------------------------------------------------------------------------
    private static final String TEXTO_BOTON_CREAR = "Crear";
    private static final String TEXTO_BOTON_EDITAR = "Editar";

    // ------------------------------------------------------------------------
    // FXML Fields
    // ------------------------------------------------------------------------
    @FXML
    private Button btnAddImage, btnAddModeloMarca, btnAddMotorMarca, btnAddNombreVehiculo,
            btnAddVersionMarca, btnBackImage, btnCrearVehiculo, btnDeleteImagen,
            btnEliminarVehiculo, btnLimpiar, btnMarca, btnNextImage, btnSalir, btnBuscarVehiculo;

    @FXML
    private SearchableComboBox<VehiculoCarroceria>  cmbCarroceria;
    @FXML
    private SearchableComboBox<VehiculoCilindrada>  cmbCilindrada;
    @FXML
    private SearchableComboBox<VehiculoMarca>       cmbMarca;
    @FXML
    private SearchableComboBox<VehiculoModelo>      cmbModelo;
    @FXML
    private SearchableComboBox<VehiculoMotor>       cmbMotor;
    @FXML
    private SearchableComboBox<VehiculoTraccion>    cmbTraccion;
    @FXML
    private SearchableComboBox<VehiculoTransmision> cmbTransimision;
    @FXML
    private SearchableComboBox<VehiculoVersion>     cmbVersion;

    @FXML
    private ImageView imgVehiculo;
    @FXML
    private ListView<VehiculoNombre> lvNombreVehiculos;

    @FXML
    private CustomTextField txtAnioFinal, txtAnioInicio;
    @FXML
    private CustomTextField txtNombreVehiculo;

    // ------------------------------------------------------------------------
    // Dependencias (Inyectadas)
    // ------------------------------------------------------------------------
    private final SpringFXMLLoader         springFXMLLoader;
    private final AlertUtil                alertUtil;
    private final VehiculoService          vehiculoService;

    // SERVICIOS AUXILIARES
    private final VehiculoBaseDataService baseDataService;
    private final VehiculoValidationService validationService;
    private final VehiculoImageManager imageManager;
    private final VehiculoNamesManager namesManager;

    // ------------------------------------------------------------------------
    // ViewModel
    // ------------------------------------------------------------------------
    private final VehiculoViewModel viewModel = new VehiculoViewModel();

    // ------------------------------------------------------------------------
    // Ciclo de vida del Controller
    // ------------------------------------------------------------------------
    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // Nombre principal => sólo lectura (se calcula en el ViewModel)
        txtNombreVehiculo.setEditable(false);

        // ListView de nombres adicionales inicialmente oculto si está vacío
        lvNombreVehiculos.setVisible(false);
        lvNombreVehiculos.setManaged(false);

        // Campos de año: sólo dígitos (validación se hace al presionar Crear/Editar)
        setupNumericFields();

        //iniciamos con un vehiculo null
        Platform.runLater(() -> initVehiculo(null));

        // Cargar datos "base" (marca, cilindrada, etc.)
        loadBaseData();

        // Enlazar ViewModel <-> Controles (combos, textFields, listView)
        setupBindings();

        // Listeners para combos dependientes (marca => modelos, motores, versiones)
        setupComboListeners();

        // Configurar la ListView de Nombres Adicionales (drag & drop, etc.)
        setupListViewNombres();

        // Ajustar la UI inicial
        refreshUIState();
    }

    /**
     * Metodo que recibe (opcionalmente) un Vehiculo para edición.
     * Si es null, se queda en modo creación.
     */
    public void initVehiculo(Vehiculo vehiculo) {
        if (vehiculo == null) {
            // Pedimos el borrador al servidor
            Disposable sub = vehiculoService.getDraftVehiculo()
                    .subscribe(
                            draft -> Platform.runLater(() -> {
                                // Asignamos el borrador al ViewModel
                                viewModel.initVehiculo(draft);

                                // Ajustamos la ventana al modo creación
                                setModoCreacion();
                                refreshUIState();
                            }),
                            e -> Platform.runLater(() -> {
                                log.error("Error al obtener borrador de Vehiculo: {}", e.getMessage());
                                alertUtil.showError(e);
                            })
                    );

            // Registramos la suscripción para limpiar luego, si corresponde (opcional).
            registerSubscription(sub);

        } else {
            // Si ya tenemos un Vehiculo (por ejemplo, en modo edición):
            viewModel.initVehiculo(vehiculo);
            setModoEdicion();
            refreshUIState();
        }
    }


    // ------------------------------------------------------------------------
    // Configuración de campos numéricos
    // ------------------------------------------------------------------------
    private void setupNumericFields() {
        // Filtro para permitir solo dígitos
        UnaryOperator<TextFormatter.Change> numericFilter =
                change -> change.getControlNewText().matches("\\d*") ? change : null;

        txtAnioInicio.setTextFormatter(new TextFormatter<>(numericFilter));
        txtAnioFinal.setTextFormatter(new TextFormatter<>(numericFilter));

        // Activar/desactivar txtAnioFinal según si txtAnioInicio está vacío
        txtAnioInicio.textProperty().addListener((obs, oldVal, newVal) ->
                txtAnioFinal.setDisable(newVal.isBlank()));
    }

    // ------------------------------------------------------------------------
    // Binding ViewModel <-> Controles
    // ------------------------------------------------------------------------
    private void setupBindings() {
        // Combos
        cmbMarca.valueProperty().bindBidirectional(viewModel.marcaProperty());
        cmbModelo.valueProperty().bindBidirectional(viewModel.modeloProperty());
        cmbMotor.valueProperty().bindBidirectional(viewModel.motorProperty());
        cmbCilindrada.valueProperty().bindBidirectional(viewModel.cilindradaProperty());
        cmbVersion.valueProperty().bindBidirectional(viewModel.versionProperty());
        cmbCarroceria.valueProperty().bindBidirectional(viewModel.carroceriaProperty());
        cmbTraccion.valueProperty().bindBidirectional(viewModel.traccionProperty());
        cmbTransimision.valueProperty().bindBidirectional(viewModel.transmisionProperty());

        // TextFields
        txtAnioInicio.textProperty().bindBidirectional(viewModel.anioInicioProperty());
        txtAnioFinal.textProperty().bindBidirectional(viewModel.anioFinalProperty());
        txtNombreVehiculo.textProperty().bindBidirectional(viewModel.nombrePrincipalProperty());

        // ListView de Nombres Adicionales
        lvNombreVehiculos.setItems(viewModel.getNombresAdicionales());
        viewModel.getNombresAdicionales().addListener(
                (ListChangeListener<VehiculoNombre>) change -> {
                    updateListViewVisibility();
                    Platform.runLater(() -> {
                        var stage = (Stage) lvNombreVehiculos.getScene().getWindow();
                        stage.sizeToScene(); // re acomoda la ventana si cambian items
                    });
                }
        );
        updateListViewVisibility();
    }

    private void updateListViewVisibility() {
        // Si no hay nombres adicionales, ocultamos la ListView
        boolean hayAdicionales = !viewModel.getNombresAdicionales().isEmpty();
        lvNombreVehiculos.setVisible(hayAdicionales);
        lvNombreVehiculos.setManaged(hayAdicionales);
    }

    // ------------------------------------------------------------------------
    // Carga de datos "base" (Marcas, Cilindradas, etc.)
    // ------------------------------------------------------------------------
    private void loadBaseData() {
        // Manejo de errores para el loadBaseData
        Consumer<Throwable> onError = e -> {
            alertUtil.showError(e);
            log.error("Error al cargar datos base: {}", e.getMessage());
        };

        baseDataService.loadMarcas(
                list -> cmbMarca.setItems(FXCollections.observableList(list)), onError
        );
        baseDataService.loadCilindradas(
                list -> cmbCilindrada.setItems(FXCollections.observableList(list)), onError
        );
        baseDataService.loadCarrocerias(
                list -> cmbCarroceria.setItems(FXCollections.observableList(list)), onError
        );
        baseDataService.loadTracciones(
                list -> cmbTraccion.setItems(FXCollections.observableList(list)), onError
        );
        baseDataService.loadTransmisiones(
                list -> cmbTransimision.setItems(FXCollections.observableList(list)), onError
        );
    }

    // ------------------------------------------------------------------------
    // Combo listeners para recargar Modelos/Motores/Versiones al cambiar Marca
    // ------------------------------------------------------------------------
    private void setupComboListeners() {
        // Cuando cambia la marca => recargamos modelos, motores y versiones
        viewModel.marcaProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                // En modo creación, desactivamos el botón BuscarVehiculo
                if (isModoCreacion()) {
                    btnBuscarVehiculo.setDisable(true);
                }
                // Cargar modelos
                baseDataService.loadModelosByMarca(
                        newVal.getId(),
                        modelos -> Platform.runLater(() -> {
                            cmbModelo.setItems(FXCollections.observableList(modelos));
                            cmbModelo.setDisable(false);
                            btnAddModeloMarca.setDisable(false);
                        }),
                        e -> mostrarErrorCarga("modelos", e)
                );

                // Cargar motores
                baseDataService.loadMotoresByMarca(
                        newVal.getId(),
                        motores -> Platform.runLater(() -> {
                            cmbMotor.setItems(FXCollections.observableList(motores));
                            cmbMotor.setDisable(false);
                            btnAddMotorMarca.setDisable(false);
                        }),
                        e -> mostrarErrorCarga("motores", e)
                );

                // Cargar versiones
                baseDataService.loadVersionesByMarca(
                        newVal.getId(),
                        versiones -> Platform.runLater(() -> {
                            cmbVersion.setItems(FXCollections.observableList(versiones));
                            cmbVersion.setDisable(false);
                            btnAddVersionMarca.setDisable(false);
                        }),
                        e -> mostrarErrorCarga("versiones", e)
                );

            } else {
                // Marca es null => en modo creación => reactivar btnBuscarVehiculo
                if (isModoCreacion()) {
                    btnBuscarVehiculo.setDisable(false);
                }
                // Deshabilitar combos dependientes
                cmbModelo.setDisable(true);
                btnAddModeloMarca.setDisable(true);

                cmbMotor.setDisable(true);
                btnAddMotorMarca.setDisable(true);

                cmbVersion.setDisable(true);
                btnAddVersionMarca.setDisable(true);
            }
            refreshUIState();
        });

        // Al cambiar el modelo o motor => refrescar UI
        viewModel.modeloProperty().addListener((obs, oldVal, newVal) -> refreshUIState());
        viewModel.motorProperty().addListener((obs, oldVal, newVal) -> refreshUIState());
    }

    /**
     * Utilidad para simplificar mostrar un error al cargar combos (modelos, motores, etc.)
     */
    private void mostrarErrorCarga(String tipo, Throwable e) {
        Platform.runLater(() -> {
            alertUtil.showError(e);
            log.error("Error al cargar {}: {}", tipo, e.getMessage());
        });
    }

    // ------------------------------------------------------------------------
    // Métodos para revisar el modo (Creación / Edición)
    // ------------------------------------------------------------------------
    private boolean isModoCreacion() {
        return TEXTO_BOTON_CREAR.equals(btnCrearVehiculo.getText());
    }

    private boolean isModoEdicion() {
        return TEXTO_BOTON_EDITAR.equals(btnCrearVehiculo.getText());
    }

    /**
     * Recalcula qué combos y botones se habilitan o deshabilitan.
     * - Marca seleccionada => activa combos dependientes
     * - Marca+Modelo+Motor => activa "Crear Vehiculo", "Add Nombre" y "Add Image"
     */
    private void refreshUIState() {
        var marcaSel  = (viewModel.marcaProperty().get()  != null);
        var modeloSel = (viewModel.modeloProperty().get() != null);
        var motorSel  = (viewModel.motorProperty().get()  != null);

        // Deshabilitamos combos si no hay marca
        cmbModelo.setDisable(!marcaSel);
        txtAnioInicio.setDisable(!marcaSel);
        if (!marcaSel) {
            txtAnioFinal.setDisable(true);
        }
        cmbMotor.setDisable(!marcaSel);
        cmbCilindrada.setDisable(!marcaSel);
        cmbVersion.setDisable(!marcaSel);
        cmbCarroceria.setDisable(!marcaSel);
        cmbTraccion.setDisable(!marcaSel);
        cmbTransimision.setDisable(!marcaSel);

        // Botones para crear Modelos, Motores, Versiones
        btnAddMotorMarca.setDisable(!marcaSel);
        btnAddModeloMarca.setDisable(!marcaSel);
        btnAddVersionMarca.setDisable(!marcaSel);

        // Marca+Modelo+Motor => habilita Crear, AddNombre, AddImage
        boolean enableMainButtons = (marcaSel && modeloSel && motorSel);
        btnCrearVehiculo.setDisable(!enableMainButtons);
        btnAddNombreVehiculo.setDisable(!enableMainButtons);
        btnAddImage.setDisable(!enableMainButtons);

        // Manejo de imágenes (eliminar/back/next)
        btnDeleteImagen.setDisable(!marcaSel);
        btnBackImage.setDisable(!marcaSel);
        btnNextImage.setDisable(!marcaSel);
    }

    // ------------------------------------------------------------------------
    // ListView de Nombres adicionales (Drag & Drop, Doble Clic, etc.)
    // ------------------------------------------------------------------------
    private void setupListViewNombres() {
        lvNombreVehiculos.setCellFactory(lv -> {
            var cell = new ListCell<VehiculoNombre>() {
                @Override
                protected void updateItem(VehiculoNombre item, boolean empty) {
                    super.updateItem(item, empty);

                    if (empty || item == null) {
                        setText(null);
                        setContextMenu(null);
                    } else {
                        setText(item.getNombre());
                        var contextMenu = new ContextMenu();

                        var miEliminar = new MenuItem("Eliminar");
                        miEliminar.setOnAction(e -> eliminarNombreVehiculo(item));

                        contextMenu.getItems().add(miEliminar);
                        setContextMenu(contextMenu);
                    }
                }
            };

            // Doble clic => editar nombre
            cell.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2 && !cell.isEmpty()) {
                    onEditarNombreVehiculo(cell.getItem());
                }
            });

            // Drag & Drop (reordenar nombres)
            cell.setOnDragDetected(event -> {
                if (!cell.isEmpty()) {
                    var db = cell.startDragAndDrop(TransferMode.MOVE);
                    var cc = new ClipboardContent();
                    cc.putString(cell.getItem().getId().toString());
                    db.setContent(cc);
                    event.consume();
                }
            });

            cell.setOnDragOver(event -> {
                if (event.getGestureSource() != cell && event.getDragboard().hasString()) {
                    event.acceptTransferModes(TransferMode.MOVE);
                }
                event.consume();
            });

            cell.setOnDragDropped(event -> {
                if (!cell.isEmpty()) {
                    var db = event.getDragboard();
                    boolean success = false;
                    if (db.hasString()) {
                        var nombreId = UUID.fromString(db.getString());
                        var draggedItem = viewModel.getNombresAdicionales().stream()
                                .filter(n -> nombreId.equals(n.getId()))
                                .findFirst()
                                .orElse(null);

                        if (draggedItem != null && draggedItem != cell.getItem()) {
                            namesManager.reorderNombreAdicional(viewModel, draggedItem, cell.getItem());
                            success = true;

                            int newIndex = viewModel.getNombresAdicionales().indexOf(draggedItem);
                            lvNombreVehiculos.getSelectionModel().clearAndSelect(newIndex);
                            lvNombreVehiculos.getFocusModel().focus(newIndex);
                        }
                    }
                    event.setDropCompleted(success);
                    event.consume();
                }
            });

            return cell;
        });
    }

    /**
     * Lógica para eliminar un nombre de vehículo tanto local como remotamente.
     */
    private void eliminarNombreVehiculo(VehiculoNombre nombre) {
        vehiculoService.deleteVehiculoNombre(nombre.getId())
                .doOnSuccess(unused -> Platform.runLater(() -> {
                    // Eliminación exitosa en servidor => remover localmente
                    namesManager.removeNombreAdicional(viewModel, nombre);
                    lvNombreVehiculos.refresh();
                }))
                .doOnError(error -> Platform.runLater(() -> {
                    // Si da error, se podría mostrar un mensaje o forzar la eliminación local
                    // alertUtil.showWarning("No se encontró en servidor, eliminando localmente...");
                    namesManager.removeNombreAdicional(viewModel, nombre);
                    lvNombreVehiculos.refresh();
                }))
                .subscribe();
    }

    private void onEditarNombreVehiculo(VehiculoNombre nombre) {
        try {
            Parent parent = springFXMLLoader.load(FXMLEnum.AGREGAR_VEHICULO_NOMBRE);
            var controller = (AgregarVehiculoNombre) springFXMLLoader.getController(parent);

            // Marca seleccionada (podría ser null)
            var marcaSeleccionada = (cmbMarca.getValue() != null)
                    ? cmbMarca.getValue().getMarca()
                    : "";

            controller.initVehiculoNombre(nombre, marcaSeleccionada);

            mostrarVentanaModal(parent, "Agregar/Editar Nombre de Vehículo", btnAddNombreVehiculo);

            lvNombreVehiculos.refresh();

        } catch (Exception e) {
            log.error("Error al abrir AgregarVehiculoNombre: {}", e.getMessage(), e);
            alertUtil.showError(e);
        }
    }

    // ------------------------------------------------------------------------
    // Botones principales (Crear, Limpiar, Eliminar, Salir, Buscar)
    // ------------------------------------------------------------------------
    @FXML
    private void onBtnCrearVehiculoClick() {
        var vehiculoAguardar = viewModel.getVehiculo();
        if (vehiculoAguardar == null) {
            return;
        }

        // Validaciones de año
        if (!validationService.validateAnioInicio(txtAnioInicio.getText())) {
            txtAnioInicio.requestFocus();
            return;
        }
        if (!validationService.validateAnioFinal(txtAnioInicio.getText(), txtAnioFinal.getText())) {
            txtAnioFinal.requestFocus();
            return;
        }

        // Guardar o actualizar
        Disposable sub = vehiculoService.saveVehiculo(vehiculoAguardar)
                .subscribe(
                        savedVehiculo -> Platform.runLater(() -> {
                            log.info("Vehículo guardado con éxito: {}", savedVehiculo);
                            // Limpiamos el formulario
                            initVehiculo(null);
                            alertUtil.showInfo("El vehículo se ha guardado correctamente.");
                        }),
                        e -> Platform.runLater(() -> {
                            alertUtil.showError(e);
                            log.error("Error al guardar vehículo: {}", e.getMessage());
                        })
                );
        registerSubscription(sub);
    }

    @FXML
    private void onBtnLimpiarClick() {
        initVehiculo(null);
        log.info("Formulario limpiado y solicitando nuevo borrador, modo creación.");
    }

    @FXML
    private void onBtnEliminarVehiculoClick() {
        var v = viewModel.getVehiculo();
        if (v == null || v.getId() == null) {
            alertUtil.showWarning("No hay un vehículo existente para eliminar.");
            return;
        }
//        var result = alertUtil.showConfirmation(
//                "Eliminar Vehículo",
//                "¿Seguro que deseas eliminar este vehículo?",
//                "Esta acción no se puede deshacer.",
//                "Eliminar",
//                "Cancelar"
//        );
//        if (result.isPresent() && result.get() == ButtonType.OK) {
//            Disposable sub = vehiculoService.deleteVehiculo(v.getId())
//                    .subscribe(
//                            unused -> Platform.runLater(() -> {
//                                log.info("Vehículo eliminado: {}", v.getId());
//                                alertUtil.showInfo("Vehículo eliminado con éxito.");
//                                onBtnLimpiarClick();
//                            }),
//                            e -> Platform.runLater(() -> {
//                                alertUtil.showError(e);
//                                log.error("Error al eliminar vehículo: {}", e.getMessage());
//                            })
//                    );
//            registerSubscription(sub);
//        }
    }

    @FXML
    private void onBtnSalirClick() {
        Stage stage = (Stage) btnSalir.getScene().getWindow();
        stage.close();
    }

    @FXML
    private void onBtnBuscarVehiculoClick() {
        try {
            Parent parent = springFXMLLoader.load(FXMLEnum.BUSCAR_VEHICULO);
            var controller = (BuscarVehiculo) springFXMLLoader.getController(parent);
            controller.setParentController(this);

            mostrarVentanaModal(parent, "Buscar Vehículo", btnBuscarVehiculo);

        } catch (Exception e) {
            log.error("Error al abrir BuscarVehiculo: {}", e.getMessage(), e);
            alertUtil.showError(e);
        }
    }

    // ------------------------------------------------------------------------
    // Botones para añadir (Nombres, Marca, Modelo, Motor, Versión)
    // ------------------------------------------------------------------------
    @FXML
    private void onBtnAddNombreVehiculoClick() {
        try {
            Parent parent = springFXMLLoader.load(FXMLEnum.AGREGAR_VEHICULO_NOMBRE);
            var controller = (AgregarVehiculoNombre) springFXMLLoader.getController(parent);

            var marcaSel = cmbMarca.getValue();
            String nombreDeMarca = (marcaSel != null) ? marcaSel.getMarca() : "";

            controller.initVehiculoNombre(null, nombreDeMarca);

            mostrarVentanaModal(parent, "Agregar/Editar Nombre de Vehículo", btnAddNombreVehiculo);

            var nuevo = controller.getVehiculoNombreCreado();
            if (nuevo != null) {
                namesManager.addNombreAdicional(viewModel, nuevo.getNombre());
                lvNombreVehiculos.refresh();
            }

        } catch (Exception e) {
            log.error("Error al abrir AgregarVehiculoNombre: {}", e.getMessage(), e);
            alertUtil.showError(e);
        }
    }

    @FXML
    private void onBtnMarcaClick() {
        var oldMarca   = cmbMarca.getValue();
        var oldModelo  = cmbModelo.getValue();
        var oldMotor   = cmbMotor.getValue();
        var oldVersion = cmbVersion.getValue();

        try {
            Parent parent = springFXMLLoader.load(FXMLEnum.CREAR_VEHICULO_MARCA);
            mostrarVentanaModal(parent, "Crear nueva Marca", btnMarca);
            refreshMarcaYsusDependientes(oldMarca, oldModelo, oldMotor, oldVersion);

        } catch (Exception e) {
            log.error("Error al abrir CrearVehiculoMarca: {}", e.getMessage(), e);
            alertUtil.showError(e);
        }
    }

    @FXML
    private void onBtnAddModeloMarcaClick() {
        var marcaSel = cmbMarca.getValue();
        if (marcaSel == null) {
            alertUtil.showError("Debe seleccionar una marca antes de crear un modelo.");
            return;
        }

        var oldMarca   = cmbMarca.getValue();
        var oldModelo  = cmbModelo.getValue();
        var oldMotor   = cmbMotor.getValue();
        var oldVersion = cmbVersion.getValue();

        try {
            Parent parent = springFXMLLoader.load(FXMLEnum.CREAR_VEHICULO_MODELO);
            var controller = (CrearVehiculoModelo) springFXMLLoader.getController(parent);
            controller.initData(marcaSel);

            mostrarVentanaModal(
                    parent,
                    "Crear/Editar Modelo: " + marcaSel.getMarca(),
                    btnAddModeloMarca
            );

            refreshMarcaYsusDependientes(oldMarca, oldModelo, oldMotor, oldVersion);

        } catch (Exception e) {
            log.error("Error al abrir CrearVehiculoModelo: {}", e.getMessage(), e);
            alertUtil.showError(e);
        }
    }

    @FXML
    private void onBtnAddMotorMarcaClick() {
        var marcaSel = cmbMarca.getValue();
        if (marcaSel == null) {
            alertUtil.showError("Debe seleccionar una marca antes de crear un motor.");
            return;
        }

        var oldMarca   = cmbMarca.getValue();
        var oldModelo  = cmbModelo.getValue();
        var oldMotor   = cmbMotor.getValue();
        var oldVersion = cmbVersion.getValue();

        try {
            Parent parent = springFXMLLoader.load(FXMLEnum.CREAR_VEHICULO_MOTOR);
            var controller = (CrearVehiculoMotor) springFXMLLoader.getController(parent);
            controller.initData(marcaSel);

            mostrarVentanaModal(
                    parent,
                    "Crear/Editar Motor: " + marcaSel.getMarca(),
                    btnAddMotorMarca
            );

            refreshMarcaYsusDependientes(oldMarca, oldModelo, oldMotor, oldVersion);

        } catch (Exception e) {
            log.error("Error al abrir CrearVehiculoMotor: {}", e.getMessage(), e);
            alertUtil.showError(e);
        }
    }

    @FXML
    private void onBtnAddVersionMarcaClick() {
        var marcaSel = cmbMarca.getValue();
        if (marcaSel == null) {
            alertUtil.showError("Debe seleccionar una marca antes de crear una versión.");
            return;
        }

        var oldMarca   = cmbMarca.getValue();
        var oldModelo  = cmbModelo.getValue();
        var oldMotor   = cmbMotor.getValue();
        var oldVersion = cmbVersion.getValue();

        try {
            Parent parent = springFXMLLoader.load(FXMLEnum.CREAR_VEHICULO_VERSION);
            var controller = (CrearVehiculoVersion) springFXMLLoader.getController(parent);
            controller.initData(marcaSel);

            mostrarVentanaModal(
                    parent,
                    "Crear/Editar Versión: " + marcaSel.getMarca(),
                    btnAddVersionMarca
            );

            refreshMarcaYsusDependientes(oldMarca, oldModelo, oldMotor, oldVersion);

        } catch (Exception e) {
            log.error("Error al abrir CrearVehiculoVersion: {}", e.getMessage(), e);
            alertUtil.showError(e);
        }
    }

    // ------------------------------------------------------------------------
    // Manejo de Imágenes (Add/Delete/Next/Back)
    // ------------------------------------------------------------------------
    @FXML
    private void onBtnAddImageClick() {
        // Ejemplo con una path dummy
        var path = "images/vehiculos/imagenDeEjemplo.jpg";
        imageManager.addImage(viewModel, path);
        imageManager.updateImageView(viewModel, imgVehiculo);
    }

    @FXML
    private void onBtnDeleteImagenClick() {
        imageManager.deleteCurrentImage(viewModel);
        imageManager.updateImageView(viewModel, imgVehiculo);
    }

    @FXML
    private void onBtnBackImageClick() {
        imageManager.goBackImage(viewModel);
        imageManager.updateImageView(viewModel, imgVehiculo);
    }

    @FXML
    private void onBtnNextImageClick() {
        imageManager.goNextImage(viewModel);
        imageManager.updateImageView(viewModel, imgVehiculo);
    }

    // ------------------------------------------------------------------------
    // Modo Creación / Edición
    // ------------------------------------------------------------------------
    private void setModoCreacion() {
        setWindowTitle("Creando Vehículo");
        btnCrearVehiculo.setText(TEXTO_BOTON_CREAR);
        btnBuscarVehiculo.setDisable(false);
        btnEliminarVehiculo.setDisable(true);
    }

    private void setModoEdicion() {
        setWindowTitle("Editando Vehículo");
        btnCrearVehiculo.setText(TEXTO_BOTON_EDITAR);
        btnBuscarVehiculo.setDisable(true);
        btnEliminarVehiculo.setDisable(false);
    }

    private void setWindowTitle(String newTitle) {
        if (btnCrearVehiculo.getScene() != null) {
            var stage = (Stage) btnCrearVehiculo.getScene().getWindow();
            stage.setTitle(newTitle);
        }
    }

    // ------------------------------------------------------------------------
    // Refresca Marcas, Modelos, Motores y Versiones sin perder la selección previa
    // ------------------------------------------------------------------------
    private void refreshMarcaYsusDependientes(VehiculoMarca oldMarca,
                                              VehiculoModelo oldModelo,
                                              VehiculoMotor oldMotor,
                                              VehiculoVersion oldVersion) {

        registerSubscription(
                vehiculoService.getAllVehiculoMarcas()
                        .collectList()
                        .subscribe(listaMarcas -> Platform.runLater(() -> {
                            // 1) Poblar combo de Marcas
                            cmbMarca.setItems(FXCollections.observableList(listaMarcas));

                            // 2) Re-seleccionar la marca anterior si aún existe
                            if (oldMarca != null) {
                                listaMarcas.stream()
                                        .filter(m -> m.getId().equals(oldMarca.getId()))
                                        .findFirst()
                                        .ifPresentOrElse(
                                                m -> cmbMarca.getSelectionModel().select(m),
                                                () -> cmbMarca.getSelectionModel().clearSelection()
                                        );
                            }

                            // 3) Volver a cargar Modelos, Motores y Versiones, y re-seleccionar
                            if (oldMarca != null) {
                                // Modelos
                                vehiculoService.getModelosByMarcaId(oldMarca.getId())
                                        .collectList()
                                        .subscribe(modelos -> Platform.runLater(() -> {
                                            cmbModelo.setItems(FXCollections.observableList(modelos));
                                            if (oldModelo != null) {
                                                modelos.stream()
                                                        .filter(x -> x.getId().equals(oldModelo.getId()))
                                                        .findFirst()
                                                        .ifPresentOrElse(
                                                                x -> cmbModelo.getSelectionModel().select(x),
                                                                () -> cmbModelo.getSelectionModel().clearSelection()
                                                        );
                                            }
                                        }));

                                // Motores
                                vehiculoService.getMotoresByMarcaId(oldMarca.getId())
                                        .collectList()
                                        .subscribe(motores -> Platform.runLater(() -> {
                                            cmbMotor.setItems(FXCollections.observableList(motores));
                                            if (oldMotor != null) {
                                                motores.stream()
                                                        .filter(x -> x.getId().equals(oldMotor.getId()))
                                                        .findFirst()
                                                        .ifPresentOrElse(
                                                                x -> cmbMotor.getSelectionModel().select(x),
                                                                () -> cmbMotor.getSelectionModel().clearSelection()
                                                        );
                                            }
                                        }));

                                // Versiones
                                vehiculoService.getVersionesByMarcaId(oldMarca.getId())
                                        .collectList()
                                        .subscribe(versiones -> Platform.runLater(() -> {
                                            cmbVersion.setItems(FXCollections.observableList(versiones));
                                            if (oldVersion != null) {
                                                versiones.stream()
                                                        .filter(x -> x.getId().equals(oldVersion.getId()))
                                                        .findFirst()
                                                        .ifPresentOrElse(
                                                                x -> cmbVersion.getSelectionModel().select(x),
                                                                () -> cmbVersion.getSelectionModel().clearSelection()
                                                        );
                                            }
                                            refreshUIState();
                                        }));
                            }
                        }), e -> {
                            Platform.runLater(() -> alertUtil.showError(e));
                            log.error("Error al refrescar marcas y dependientes: {}", e.getMessage());
                        })
        );
    }

    // ------------------------------------------------------------------------
    // Métodos privados de utilidad para reducir duplicidad
    // ------------------------------------------------------------------------
    /**
     * Muestra una ventana modal con título y la asocia a una ventana “padre”.
     */
    private void mostrarVentanaModal(Parent parent, String titulo, Control parentControl) {
        var stage = new Stage();
        stage.initModality(Modality.WINDOW_MODAL);
        stage.initOwner(parentControl.getScene().getWindow());
        stage.setTitle(titulo);
        stage.setScene(new Scene(parent));
        stage.showAndWait();
    }
}
// recuerda siempre Manejar el onComplete ya que el servidor retorna un Mono<Void> el cliente no va a recibir un “objeto” en el subscribe(...),
// sino simplemente una señal de onComplete cuando ha salido bien. Dicho de otro modo, no vas a tener un onNext(...) con un valor “unused”; en realidad,
// en Mono<Void> no se emite ningún valor, sólo se dispara onComplete.