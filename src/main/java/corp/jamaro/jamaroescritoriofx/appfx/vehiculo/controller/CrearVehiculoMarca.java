package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoMarca;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.VehiculoService;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.stage.Stage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.CustomTextField;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.UUID;

/**
 * Controlador para la ventana de creación/edición de Marcas (VehiculoMarca).
 * - Crea o edita una marca (doble clic => edición).
 * - Elimina una marca con confirmación.
 * - Usa AlertUtil para todas las alertas con estilo unificado.
 *
 * Adaptado para usar ListView en lugar de TableView.
 */
@Slf4j
@Component
@Scope("prototype")
@RequiredArgsConstructor
public class CrearVehiculoMarca extends BaseController {

    @FXML
    private CustomTextField txtMarca;

    /**
     * ListView para mostrar las marcas en vez del TableView.
     */
    @FXML
    private ListView<VehiculoMarca> lvResultados;

    @FXML
    private Button btnGuardar;
    @FXML
    private Button btnSalir;
    @FXML
    private Button btnLimpiar;

    private final VehiculoService vehiculoService;
    private final AlertUtil alertUtil;

    /**
     * Marca en edición. Si es null, se está creando una nueva.
     */
    private VehiculoMarca marcaEnEdicion;

    // ------------------------------------------------------------------------
    // Inicialización
    // ------------------------------------------------------------------------
    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        configurarListView();
        cargarMarcas();

        // Evita NullPointerException, llamando a setModoCreacion() tras inicializar la escena
        Platform.runLater(this::setModoCreacion);
    }

    // ------------------------------------------------------------------------
    // Configuración de la ListView
    // ------------------------------------------------------------------------
    /**
     * Configura la ListView para:
     * - mostrar toString() de cada VehiculoMarca
     * - doble clic para cargar en edición
     * - menú contextual para eliminar
     */
    private void configurarListView() {
        // Usamos el CellFactory para establecer el texto y la interacción
        lvResultados.setCellFactory(listView -> {
            ListCell<VehiculoMarca> cell = new ListCell<>() {

                // Menú contextual para "Eliminar"
                private final ContextMenu contextMenu = new ContextMenu();

                {
                    MenuItem itemEliminar = new MenuItem("Eliminar");
                    itemEliminar.setOnAction(e -> {
                        if (!isEmpty()) {
                            onEliminarMarcaClick(getItem());
                        }
                    });
                    contextMenu.getItems().add(itemEliminar);

                    // Doble clic => cargar marca en edición
                    setOnMouseClicked(event -> {
                        if (event.getClickCount() == 2 && !isEmpty()) {
                            cargarMarcaEnEdicion(getItem());
                        }
                    });
                }

                @Override
                protected void updateItem(VehiculoMarca item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty || item == null) {
                        setText(null);
                        setContextMenu(null);
                    } else {
                        // Mostramos el toString() => la propiedad 'marca'
                        setText(item.toString());
                        setContextMenu(contextMenu);
                    }
                }
            };
            return cell;
        });
    }

    // ------------------------------------------------------------------------
    // Carga de datos
    // ------------------------------------------------------------------------
    /**
     * Carga todas las marcas desde el servidor y las muestra en la ListView.
     */
    private void cargarMarcas() {
        Disposable sub = vehiculoService.getAllVehiculoMarcas()
                .collectList()
                .subscribe(
                        lista -> Platform.runLater(() -> {
                            lvResultados.setItems(FXCollections.observableList(lista));
                        }),
                        error -> Platform.runLater(() -> {
                            alertUtil.showError(error);
                            log.error("Error al cargar marcas: {}", error.getMessage(), error);
                        })
                );
        registerSubscription(sub);
    }

    // ------------------------------------------------------------------------
    // Manejo de creación/edición
    // ------------------------------------------------------------------------
    /**
     * Entra en modo edición, cargando los datos de la marca en la UI.
     */
    private void cargarMarcaEnEdicion(VehiculoMarca marca) {
        this.marcaEnEdicion = marca;
        txtMarca.setText(marca.getMarca());
        setModoEdicion();
        log.debug("Marca en edición: {}", marca);
    }

    /**
     * Pasa a "modo creación": limpia el campo de texto y ajusta el botón Guardar.
     */
    private void setModoCreacion() {
        marcaEnEdicion = null;
        txtMarca.clear();
        btnGuardar.setText("Crear");
        setWindowTitle("Creando Marca");
    }

    /**
     * Pasa a "modo edición": cambia el botón a "Editar".
     */
    private void setModoEdicion() {
        btnGuardar.setText("Editar");
        setWindowTitle("Editando Marca");
    }

    /**
     * Ajusta el título de la ventana.
     */
    private void setWindowTitle(String newTitle) {
        Stage stage = (Stage) btnGuardar.getScene().getWindow();
        stage.setTitle(newTitle);
    }

    // ------------------------------------------------------------------------
    // Manejo de eliminación
    // ------------------------------------------------------------------------
    /**
     * Muestra confirmación antes de eliminar la marca.
     */
    private void onEliminarMarcaClick(VehiculoMarca marca) {
        Optional<ButtonType> result = alertUtil.showConfirmation(
                "Confirmar eliminación",
                "¿Seguro que desea eliminar la marca?",
                "Marca: " + marca.getMarca(),
                "Sí",
                "No"
        );
        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            eliminarMarca(marca);
        }
    }

    /**
     * Elimina la marca en el servidor. Al completarse, recarga la lista y muestra un mensaje.
     */
    private void eliminarMarca(VehiculoMarca marca) {
        Disposable sub = vehiculoService.deleteVehiculoMarca(marca.getId())
                .subscribe(
                        null, // Mono<Void>, no hay valor de respuesta
                        error -> Platform.runLater(() -> {
                            alertUtil.showError(error);
                            log.error("Error al eliminar la marca: {}", error.getMessage(), error);
                        }),
                        () -> Platform.runLater(() -> {
                            log.info("Marca eliminada con éxito: {}", marca);
                            alertUtil.showInfo("La marca \"" + marca.getMarca() + "\" se eliminó correctamente.");
                            cargarMarcas(); // recargar la lista
                        })
                );
        registerSubscription(sub);
    }

    // ------------------------------------------------------------------------
    // Botones
    // ------------------------------------------------------------------------
    @FXML
    private void onBtnGuardarClick() {
        String nombre = txtMarca.getText().trim();
        if (nombre.isEmpty()) {
            alertUtil.showError("Debe ingresar un nombre de marca antes de guardar.");
            return;
        }

        boolean esCreacion = (marcaEnEdicion == null);
        if (esCreacion) {
            marcaEnEdicion = new VehiculoMarca();
            marcaEnEdicion.setId(UUID.randomUUID());
        }
        marcaEnEdicion.setMarca(nombre);

        Disposable sub = vehiculoService.saveVehiculoMarca(marcaEnEdicion)
                .subscribe(
                        saved -> Platform.runLater(() -> {
                            String operacion = esCreacion ? "creada" : "actualizada";
                            log.info("Marca {} con éxito: {}", operacion, saved);
                            alertUtil.showInfo("La marca \"" + saved.getMarca()
                                    + "\" fue " + operacion + " correctamente.");

                            cargarMarcas();
                            setModoCreacion();
                        }),
                        error -> Platform.runLater(() -> {
                            alertUtil.showError(error);
                            log.error("Error al {} la marca: {}", esCreacion ? "crear" : "actualizar", error.getMessage());
                        })
                );
        registerSubscription(sub);
    }

    @FXML
    private void onBtnLimpiarClick() {
        setModoCreacion();
    }

    @FXML
    private void onBtnSalirClick() {
        Stage stage = (Stage) btnSalir.getScene().getWindow();
        stage.close();
    }
}
