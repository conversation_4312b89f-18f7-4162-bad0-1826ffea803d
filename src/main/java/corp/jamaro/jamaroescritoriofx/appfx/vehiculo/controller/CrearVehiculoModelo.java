package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoMarca;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoModelo;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.VehiculoService;
import javafx.application.Platform;
import javafx.beans.binding.Bindings;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Stage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.CustomTextField;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.UUID;

/**
 * Controlador para la ventana de creación/edición de Modelos (VehiculoModelo),
 * asociado a una marca específica.
 *
 * Muestra la marca en un label a la izquierda de txtModelo,
 * para que el usuario solo edite la parte variable.
 */
@Slf4j
@Component
@Scope("prototype")
@RequiredArgsConstructor
public class CrearVehiculoModelo extends BaseController {

    // =========================================================================
    //                           FXML INJECTIONS
    // =========================================================================
    @FXML
    private CustomTextField txtModelo;  // Para escribir solo la parte variable (p.ej., "probox")

    @FXML
    private TableView<VehiculoModelo> tvResultados;

    @FXML
    private TableColumn<VehiculoModelo, String> colModelo;

    @FXML
    private Button btnGuardar, btnLimpiar, btnSalir;

    // =========================================================================
    //                           DEPENDENCIAS
    // =========================================================================
    private final VehiculoService vehiculoService;
    private final AlertUtil alertUtil;

    /**
     * Marca asociada, para la cual estamos manejando los modelos.
     */
    private VehiculoMarca marcaAsociada;

    /**
     * Modelo en edición. Si es null => modo creación.
     */
    private VehiculoModelo modeloEnEdicion;

    /**
     * Label para mostrar la marca a la izquierda del txtModelo.
     */
    private Label lblMarca;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // Configura la columna para mostrar "modelo" (getter: getModelo())
        colModelo.setCellValueFactory(new PropertyValueFactory<>("modelo"));
        tvResultados.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY_ALL_COLUMNS);

        configurarRowFactory();

        // Modo creación por defecto
        modeloEnEdicion = null;
        btnGuardar.setText("Crear");

        // Creamos el label que muestra la marca al costado izquierdo del txtModelo
        lblMarca = new Label();
        // Asignamos ese label a la "Left" de este CustomTextField
        txtModelo.setLeft(lblMarca);
    }

    // =========================================================================
    //                           PUBLIC METHODS
    // =========================================================================

    /**
     * Inicializa con la marca dada, ajusta título y carga lista de modelos.
     */
    public void initData(VehiculoMarca vehiculoMarca) {
        this.marcaAsociada = vehiculoMarca;
        if (marcaAsociada != null) {
            // Mostramos la marca en el label (con un espacio al final)
            lblMarca.setText(marcaAsociada.getMarca() + " ");

            // Evitamos NullPointerException en setWindowTitle
            Platform.runLater(() -> {
                setWindowTitle("Modelos de la marca: " + marcaAsociada.getMarca() + " - Creando Modelo");
            });

            cargarModelos();
        } else {
            log.warn("initData() se llamó con vehiculoMarca == null");
        }
    }

    // =========================================================================
    //                           PRIVATE METHODS
    // =========================================================================

    /**
     * rowFactory: doble clic => editar, menú contextual => eliminar.
     */
    private void configurarRowFactory() {
        tvResultados.setRowFactory(tableView -> {
            TableRow<VehiculoModelo> row = new TableRow<>();

            // Menú contextual "Eliminar"
            MenuItem itemEliminar = new MenuItem("Eliminar");
            itemEliminar.setOnAction(e -> {
                if (!row.isEmpty()) {
                    onEliminarModeloClick(row.getItem());
                }
            });
            ContextMenu ctxMenu = new ContextMenu(itemEliminar);

            // Mostrar menú solo en filas no vacías
            row.contextMenuProperty().bind(
                    Bindings.when(row.emptyProperty())
                            .then((ContextMenu) null)
                            .otherwise(ctxMenu)
            );

            // Doble clic => editar
            row.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2 && !row.isEmpty()) {
                    cargarModeloEnEdicion(row.getItem());
                }
            });

            return row;
        });
    }

    /**
     * Carga los modelos de marcaAsociada en tvResultados.
     */
    private void cargarModelos() {
        if (marcaAsociada == null) {
            log.warn("No se puede cargar modelos: marcaAsociada es null");
            return;
        }
        Disposable sub = vehiculoService.getModelosByMarcaId(marcaAsociada.getId())
                .collectList()
                .subscribe(
                        lista -> Platform.runLater(() -> {
                            tvResultados.setItems(FXCollections.observableList(lista));
                            tvResultados.refresh();
                        }),
                        error -> Platform.runLater(() -> {
                            alertUtil.showError(error);
                            log.error("Error al cargar modelos de la marca {}: {}", marcaAsociada.getId(), error.getMessage());
                        })
                );
        registerSubscription(sub);
    }

    /**
     * Activa el modo edición, separando la parte de la marca para que el usuario
     * solo vea y edite la parte final.
     */
    private void cargarModeloEnEdicion(VehiculoModelo modelo) {
        this.modeloEnEdicion = modelo;
        setModoEdicion();

        // Suponemos que en la base se guardó "toyota probox"
        // y "toyota " es lblMarca.getText().
        // Quitamos el prefijo "toyota " del string final:
        String fullName = modelo.getModelo();
        String prefixMarca = lblMarca.getText(); // Ej. "toyota "

        String parteEditable = fullName;
        if (prefixMarca != null && !prefixMarca.isBlank()) {
            // Si el string inicia con "toyota "
            if (fullName.startsWith(prefixMarca)) {
                // Recortamos esa parte
                parteEditable = fullName.substring(prefixMarca.length());
            }
        }
        txtModelo.setText(parteEditable);

        log.debug("Modelo en edición: {} => parteEditable: {}", fullName, parteEditable);
    }

    /**
     * Diálogo de confirmación antes de eliminar.
     */
    private void onEliminarModeloClick(VehiculoModelo modelo) {
        Optional<ButtonType> result = alertUtil.showConfirmation(
                "Confirmar eliminación",
                "¿Desea eliminar este modelo?",
                "Modelo: " + modelo.getModelo(),
                "Sí",
                "No"
        );
        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            eliminarModelo(modelo);
        }
    }

    /**
     * Elimina un modelo en el servidor y recarga la lista.
     */
    private void eliminarModelo(VehiculoModelo modelo) {
        Disposable sub = vehiculoService.deleteVehiculoModelo(modelo.getId())
                .subscribe(
                        null,
                        error -> Platform.runLater(() -> {
                            alertUtil.showError(error);
                            log.error("Error al eliminar modelo: {}", error.getMessage());
                        }),
                        () -> Platform.runLater(() -> {
                            log.info("Modelo eliminado con éxito: {}", modelo);
                            alertUtil.showInfo("El modelo \"" + modelo.getModelo() + "\" se eliminó correctamente.");
                            cargarModelos();
                        })
                );
        registerSubscription(sub);
    }

    /**
     * Modo creación: se limpia txtModelo, se pone "Crear", y ajusta el título.
     */
    private void setModoCreacion() {
        modeloEnEdicion = null;
        txtModelo.clear();
        btnGuardar.setText("Crear");
        if (marcaAsociada != null) {
            Platform.runLater(() ->
                    setWindowTitle("Modelos de la marca: " + marcaAsociada.getMarca() + " - Creando Modelo")
            );
        }
    }

    /**
     * Modo edición: se ajusta el botón a "Editar" y el título a "Editando Modelo".
     */
    private void setModoEdicion() {
        btnGuardar.setText("Editar");
        if (marcaAsociada != null) {
            Platform.runLater(() ->
                    setWindowTitle("Modelos de la marca: " + marcaAsociada.getMarca() + " - Editando Modelo")
            );
        }
    }

    /**
     * Cambia el título de la ventana (evitando NPE si la escena no está lista).
     */
    private void setWindowTitle(String newTitle) {
        Stage stage = (Stage) btnGuardar.getScene().getWindow();
        stage.setTitle(newTitle);
    }

    // =========================================================================
    //                           BUTTON HANDLERS
    // =========================================================================

    /**
     * Crea o edita un modelo. Si es nuevo, se llama addVehiculoModeloToMarca.
     * Si es existente, se llama saveVehiculoModelo.
     */
    @FXML
    private void onBtnGuardarClick() {
        // Validar texto
        String parteEditable = txtModelo.getText().trim();
        if (parteEditable.isEmpty()) {
            alertUtil.showError("Debe ingresar el nombre del modelo antes de guardar.");
            return;
        }
        if (marcaAsociada == null) {
            alertUtil.showError("No se ha definido la marca asociada.");
            return;
        }

        boolean esCreacion = (modeloEnEdicion == null);

        // Armamos el nombre final => "toyota " + "probox" => "toyota probox"
        String prefix = lblMarca.getText(); // Ej. "toyota "
        if (prefix == null) prefix = "";
        String nombreCompleto = (prefix + parteEditable).trim(); // Ej. "toyota probox"

        if (esCreacion) {
            // MODO CREACIÓN
            VehiculoModelo nuevo = new VehiculoModelo();
            nuevo.setId(UUID.randomUUID());
            nuevo.setModelo(nombreCompleto);

            Disposable sub = vehiculoService.addVehiculoModeloToMarca(marcaAsociada.getId(), nuevo)
                    .subscribe(
                            // addVehiculoModeloToMarca => Mono<VehiculoMarca>
                            vehiculoMarcaActualizada -> Platform.runLater(() -> {
                                log.info("Modelo creado y agregado a marca con éxito: {}", nuevo);
                                alertUtil.showInfo("El modelo \"" + nombreCompleto + "\" fue creado correctamente.");
                                cargarModelos();
                                setModoCreacion();
                            }),
                            error -> Platform.runLater(() -> {
                                alertUtil.showError(error);
                                log.error("Error al crear el modelo: {}", error.getMessage());
                            })
                    );
            registerSubscription(sub);

        } else {
            // MODO EDICIÓN
            // Actualizamos el "modelo" (nombreCompleto)
            modeloEnEdicion.setModelo(nombreCompleto);

            Disposable sub = vehiculoService.saveVehiculoModelo(modeloEnEdicion)
                    .subscribe(
                            saved -> Platform.runLater(() -> {
                                log.info("Modelo editado con éxito: {}", saved);
                                alertUtil.showInfo("El modelo \"" + nombreCompleto + "\" fue actualizado correctamente.");
                                cargarModelos();
                                setModoCreacion();
                            }),
                            error -> Platform.runLater(() -> {
                                alertUtil.showError(error);
                                log.error("Error al actualizar el modelo: {}", error.getMessage());
                            })
                    );
            registerSubscription(sub);
        }
    }

    /**
     * Limpia el formulario => modo creación.
     */
    @FXML
    private void onBtnLimpiarClick() {
        setModoCreacion();
    }

    /**
     * Cierra la ventana.
     */
    @FXML
    private void onBtnSalirClick() {
        Stage stage = (Stage) btnSalir.getScene().getWindow();
        stage.close();
    }
}
