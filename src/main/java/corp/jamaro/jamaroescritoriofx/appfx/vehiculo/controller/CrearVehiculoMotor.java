package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.TipoDeMotor;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoMarca;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoMotor;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.VehiculoService;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.ButtonBar.ButtonData;
import javafx.stage.Stage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.SearchableComboBox;
import org.controlsfx.control.textfield.CustomTextField;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.UUID;

/**
 * Controlador para la ventana de creación/edición de Motores (VehiculoMotor),
 * asociado a una marca en particular.
 *
 * Este controlador:
 *   - Permite crear o editar un VehiculoMotor asociado a una VehiculoMarca.
 *   - Muestra un ListView (lvResultados) con los motores existentes.
 *   - En el ListView, el metodo toString() de VehiculoMotor define lo que se muestra.
 *   - Con doble clic en un elemento del ListView se puede editar.
 *   - Con menú contextual (clic derecho) se puede eliminar.
 *   - Se cuenta con campos txtMotor y cmbTipoMotor para ingresar datos.
 */
@Slf4j
@Component
@Scope("prototype")
@RequiredArgsConstructor
public class CrearVehiculoMotor extends BaseController {

    // =========================================================================
    //                           FXML INJECTIONS
    // =========================================================================
    @FXML
    private CustomTextField txtMotor; // Usuario ingresa "4E", "5E", etc.

    @FXML
    private SearchableComboBox<TipoDeMotor> cmbTipoMotor; // Gasolina, Diésel, Híbrido, etc.

    /**
     * ListView para mostrar los motores existentes.
     * Aquí aprovecharemos el toString() de VehiculoMotor.
     */
    @FXML
    private ListView<VehiculoMotor> lvResultados;

    @FXML
    private Button btnGuardar, btnLimpiar, btnSalir;

    // =========================================================================
    //                           DEPENDENCIAS
    // =========================================================================
    private final VehiculoService vehiculoService;
    private final AlertUtil alertUtil;

    /**
     * Marca a la cual se asociarán estos motores.
     */
    private VehiculoMarca marcaAsociada;

    /**
     * Motor en edición. Si es null => estamos creando uno nuevo.
     */
    private VehiculoMotor motorEnEdicion;

    /**
     * Label para mostrar la marca a la izquierda del txtMotor (sólo informativo).
     * Lo creamos en tiempo de ejecución y se asigna a txtMotor.setLeft(label).
     */
    private Label lblMarca;

    // =========================================================================
    //                           INITIALIZE
    // =========================================================================
    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // 1) Modo creación por defecto
        motorEnEdicion = null;
        btnGuardar.setText("Crear");

        // 2) Creamos un Label para mostrar la marca a la izquierda del txtMotor
        lblMarca = new Label();
        txtMotor.setLeft(lblMarca);

        // 3) Cargar la lista de TipoDeMotor en cmbTipoMotor (asíncronamente)
        cargarTiposDeMotor();

        // 4) Configurar la ListView => CellFactory + lógica de doble clic y menú contextual
        configurarListView();
    }

    // =========================================================================
    //                           PUBLIC METHODS
    // =========================================================================
    /**
     * Inicializa este formulario con la marca.
     * Ajusta el título de la ventana y carga la lista de motores.
     *
     * @param vehiculoMarca Marca a la cual se asociarán los motores.
     */
    public void initData(VehiculoMarca vehiculoMarca) {
        this.marcaAsociada = vehiculoMarca;
        if (marcaAsociada != null) {
            lblMarca.setText(marcaAsociada.getMarca() + " ");

            // Ajustamos el título de la ventana
            Platform.runLater(() ->
                    setWindowTitle("Motores de la marca: " + marcaAsociada.getMarca() + " - Creando Motor")
            );

            // Cargamos la lista de motores asociados a dicha marca
            cargarMotores();
        } else {
            log.warn("initData() se llamó con vehiculoMarca == null");
        }
    }

    // =========================================================================
    //                           PRIVATE METHODS
    // =========================================================================

    /**
     * Configura la ListView para que muestre los datos y maneje
     * la edición por doble clic y el borrado por menú contextual.
     */
    private void configurarListView() {
        lvResultados.setCellFactory(listView -> {
            return new ListCell<>() {
                // Menú contextual para eliminar
                private final ContextMenu contextMenu = new ContextMenu();

                {
                    MenuItem itemEliminar = new MenuItem("Eliminar");
                    itemEliminar.setOnAction(e -> {
                        if (!isEmpty()) {
                            onEliminarMotorClick(getItem());
                        }
                    });
                    contextMenu.getItems().add(itemEliminar);

                    // Doble clic para editar
                    setOnMouseClicked(event -> {
                        if (event.getClickCount() == 2 && !isEmpty()) {
                            cargarMotorEnEdicion(getItem());
                        }
                    });
                }

                @Override
                protected void updateItem(VehiculoMotor item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty || item == null) {
                        setText(null);
                        setContextMenu(null);
                    } else {
                        // Mostramos el texto usando VehiculoMotor.toString()
                        setText(item.toString());
                        setContextMenu(contextMenu);
                    }
                }
            };
        });
    }

    /**
     * Carga todos los motores asociados a la marcaAsociada en la ListView.
     */
    private void cargarMotores() {
        if (marcaAsociada == null) {
            log.warn("cargarMotores(): marcaAsociada es null, no se puede cargar motores.");
            return;
        }
        Disposable sub = vehiculoService.getMotoresByMarcaId(marcaAsociada.getId())
                .collectList()
                .subscribe(
                        lista -> Platform.runLater(() -> {
                            lvResultados.setItems(FXCollections.observableList(lista));
                            // Normalmente no necesitas llamar lvResultados.refresh(),
                            // pero puedes hacerlo si deseas forzar un refresco.
                        }),
                        error -> Platform.runLater(() -> {
                            alertUtil.showError(error);
                            log.error("Error al cargar motores de la marca {}: {}", marcaAsociada.getId(), error.getMessage());
                        })
                );
        registerSubscription(sub);
    }

    /**
     * Carga la lista de tipos de motor (gasolina, diésel, etc.) en cmbTipoMotor.
     */
    private void cargarTiposDeMotor() {
        Disposable sub = vehiculoService.getAllTipoDeMotor()
                .collectList()
                .subscribe(
                        lista -> Platform.runLater(() -> {
                            cmbTipoMotor.setItems(FXCollections.observableList(lista));
                        }),
                        error -> Platform.runLater(() -> {
                            alertUtil.showError(error);
                            log.error("Error al cargar tipos de motor: {}", error.getMessage());
                        })
                );
        registerSubscription(sub);
    }

    /**
     * Carga un motor en la UI para ser editado (modo edición).
     *
     * @param motor el motor que se va a editar.
     */
    private void cargarMotorEnEdicion(VehiculoMotor motor) {
        this.motorEnEdicion = motor;
        setModoEdicion();

        // Ejemplo: "4E" en txtMotor
        txtMotor.setText(motor.getMotor());
        // Seleccionamos el tipo de motor si existe
        if (motor.getTipoDeMotor() != null) {
            cmbTipoMotor.setValue(motor.getTipoDeMotor());
        } else {
            cmbTipoMotor.setValue(null);
        }

        log.debug("Motor en edición: {}", motor.getMotor());
    }

    /**
     * Muestra confirmación antes de eliminar un motor.
     */
    private void onEliminarMotorClick(VehiculoMotor motor) {
        Optional<ButtonType> result = alertUtil.showConfirmation(
                "Confirmar eliminación",
                "¿Desea eliminar este motor?",
                "Motor: " + motor.getMotor(),
                "Sí",
                "No"
        );
        if (result.isPresent() && result.get().getButtonData() == ButtonData.OK_DONE) {
            eliminarMotor(motor);
        }
    }

    /**
     * Elimina el motor en el servidor y recarga la lista.
     */
    private void eliminarMotor(VehiculoMotor motor) {
        Disposable sub = vehiculoService.deleteVehiculoMotor(motor.getId())
                .subscribe(
                        null,
                        error -> Platform.runLater(() -> {
                            alertUtil.showError(error);
                            log.error("Error al eliminar motor: {}", error.getMessage());
                        }),
                        () -> Platform.runLater(() -> {
                            log.info("Motor eliminado con éxito: {}", motor);
                            alertUtil.showInfo("El motor \"" + motor.getMotor() + "\" se eliminó correctamente.");
                            cargarMotores();
                        })
                );
        registerSubscription(sub);
    }

    /**
     * Modo creación: limpia txtMotor, pone "Crear", ajusta título a "Creando Motor".
     */
    private void setModoCreacion() {
        motorEnEdicion = null;
        txtMotor.clear();
        cmbTipoMotor.setValue(null);
        btnGuardar.setText("Crear");
        if (marcaAsociada != null) {
            Platform.runLater(() ->
                    setWindowTitle("Motores de la marca: " + marcaAsociada.getMarca() + " - Creando Motor")
            );
        }
    }

    /**
     * Modo edición: pone "Editar", ajusta título a "Editando Motor".
     */
    private void setModoEdicion() {
        btnGuardar.setText("Editar");
        if (marcaAsociada != null) {
            Platform.runLater(() ->
                    setWindowTitle("Motores de la marca: " + marcaAsociada.getMarca() + " - Editando Motor")
            );
        }
    }

    /**
     * Cambia el título de la ventana (evitando NPE).
     */
    private void setWindowTitle(String newTitle) {
        Stage stage = (Stage) btnGuardar.getScene().getWindow();
        stage.setTitle(newTitle);
    }

    // =========================================================================
    //                           BUTTON HANDLERS
    // =========================================================================
    /**
     * Maneja la acción de guardar (crear/editar) un motor.
     */
    @FXML
    private void onBtnGuardarClick() {
        String motorIngresado = txtMotor.getText().trim();
        if (motorIngresado.isEmpty()) {
            alertUtil.showError("Debe ingresar el nombre del motor antes de guardar.");
            return;
        }
        if (marcaAsociada == null) {
            alertUtil.showError("No se ha definido la marca asociada.");
            return;
        }

        // TipoDeMotor seleccionado
        TipoDeMotor tipoSel = cmbTipoMotor.getValue();

        boolean esCreacion = (motorEnEdicion == null);

        if (esCreacion) {
            // --- MODO CREACIÓN ---
            VehiculoMotor nuevo = new VehiculoMotor();
            nuevo.setId(UUID.randomUUID());
            nuevo.setMotor(motorIngresado);
            nuevo.setTipoDeMotor(tipoSel);

            // addVehiculoMotorToMarca => Mono<VehiculoMarca>
            Disposable sub = vehiculoService.addVehiculoMotorToMarca(marcaAsociada.getId(), nuevo)
                    .subscribe(
                            vehiculoMarcaActualizada -> Platform.runLater(() -> {
                                log.info("Motor creado y agregado a la marca con éxito: {}", nuevo);
                                alertUtil.showInfo("El motor \"" + motorIngresado + "\" fue creado correctamente.");
                                cargarMotores();
                                setModoCreacion();
                            }),
                            error -> Platform.runLater(() -> {
                                alertUtil.showError(error);
                                log.error("Error al crear el motor: {}", error.getMessage());
                            })
                    );
            registerSubscription(sub);

        } else {
            // --- MODO EDICIÓN ---
            motorEnEdicion.setMotor(motorIngresado);
            motorEnEdicion.setTipoDeMotor(tipoSel);

            // saveVehiculoMotor => Mono<VehiculoMotor>
            Disposable sub = vehiculoService.saveVehiculoMotor(motorEnEdicion)
                    .subscribe(
                            saved -> Platform.runLater(() -> {
                                log.info("Motor editado con éxito: {}", saved);
                                alertUtil.showInfo("El motor \"" + motorIngresado + "\" fue actualizado correctamente.");
                                cargarMotores();
                                setModoCreacion();
                            }),
                            error -> Platform.runLater(() -> {
                                alertUtil.showError(error);
                                log.error("Error al actualizar el motor: {}", error.getMessage());
                            })
                    );
            registerSubscription(sub);
        }
    }

    /**
     * Maneja la acción de limpiar/crear nuevo motor.
     * Retorna al modo creación.
     */
    @FXML
    private void onBtnLimpiarClick() {
        setModoCreacion();
    }

    /**
     * Maneja la acción de salir.
     */
    @FXML
    private void onBtnSalirClick() {
        Stage stage = (Stage) btnSalir.getScene().getWindow();
        stage.close();
    }
}
