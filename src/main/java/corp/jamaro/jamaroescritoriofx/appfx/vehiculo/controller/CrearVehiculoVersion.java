package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoMarca;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoVersion;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.VehiculoService;
import javafx.application.Platform;
import javafx.beans.binding.Bindings;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Stage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.CustomTextField;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.UUID;

/**
 * Controlador para la ventana de creación/edición de Versiones (VehiculoVersion),
 * asociado a una marca en particular.
 *
 * - Muestra la marca en un Label a la izquierda de txtVersion (sólo informativo).
 * - txtVersion para el texto de la versión (p.ej. "GL", "XLE", "Sport").
 * - txtDescripcion para un texto adicional sobre la versión.
 */
@Slf4j
@Component
@Scope("prototype")
@RequiredArgsConstructor
public class CrearVehiculoVersion extends BaseController {

    // =========================================================================
    //                           FXML INJECTIONS
    // =========================================================================
    @FXML
    private CustomTextField txtVersion;  // Campo principal (e.g., "GL", "XLE", "Sport")

    @FXML
    private TextArea txtDescripcion;     // Campo adicional para descripción

    @FXML
    private TableView<VehiculoVersion> tvResultados;

    @FXML
    private TableColumn<VehiculoVersion, String> colVersion; // Muestra la propiedad "version"

    @FXML
    private Button btnGuardar, btnLimpiar, btnSalir;

    // =========================================================================
    //                           DEPENDENCIAS
    // =========================================================================
    private final VehiculoService vehiculoService;
    private final AlertUtil alertUtil;

    /**
     * Marca a la cual se asociarán estas versiones.
     */
    private VehiculoMarca marcaAsociada;

    /**
     * Versión en edición. Si es null => estamos creando una nueva.
     */
    private VehiculoVersion versionEnEdicion;

    /**
     * Label decorativo para mostrar la marca a la izquierda de txtVersion.
     */
    private Label lblMarca;

    // =========================================================================
    //                           INITIALIZE
    // =========================================================================
    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // 1) Configurar la columna de la tabla para mostrar "version"
        colVersion.setCellValueFactory(new PropertyValueFactory<>("version"));
        tvResultados.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY_ALL_COLUMNS);

        // 2) rowFactory => doble clic (editar) + menú contextual (eliminar)
        configurarRowFactory();

        // 3) Modo creación por defecto
        versionEnEdicion = null;
        btnGuardar.setText("Crear");

        // 4) Creamos un Label para mostrar la marca a la izquierda de txtVersion
        lblMarca = new Label();
        txtVersion.setLeft(lblMarca);
    }

    // =========================================================================
    //                           PUBLIC METHODS
    // =========================================================================

    /**
     * Inicializa este formulario con la marca asociada.
     * Ajusta el título y carga la lista de versiones.
     *
     * @param vehiculoMarca Marca a la que se asociarán las versiones.
     */
    public void initData(VehiculoMarca vehiculoMarca) {
        this.marcaAsociada = vehiculoMarca;
        if (marcaAsociada != null) {
            // Mostramos la marca a la izquierda
            lblMarca.setText(marcaAsociada.getMarca() + " ");

            // Ajustamos título (runLater para evitar NPE de la escena)
            Platform.runLater(() -> {
                setWindowTitle("Versiones de la marca: " + marcaAsociada.getMarca() + " - Creando Versión");
            });

            cargarVersiones();
        } else {
            log.warn("initData() se llamó con vehiculoMarca == null");
        }
    }

    // =========================================================================
    //                           PRIVATE METHODS
    // =========================================================================

    /**
     * Configura rowFactory: doble clic => editar, menú contextual => eliminar.
     */
    private void configurarRowFactory() {
        tvResultados.setRowFactory(tableView -> {
            TableRow<VehiculoVersion> row = new TableRow<>();

            // Menú contextual: "Eliminar"
            MenuItem itemEliminar = new MenuItem("Eliminar");
            itemEliminar.setOnAction(e -> {
                if (!row.isEmpty()) {
                    onEliminarVersionClick(row.getItem());
                }
            });
            ContextMenu ctxMenu = new ContextMenu(itemEliminar);

            // Mostrar menú solo en filas no vacías
            row.contextMenuProperty().bind(
                    Bindings.when(row.emptyProperty())
                            .then((ContextMenu) null)
                            .otherwise(ctxMenu)
            );

            // Doble clic => editar
            row.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2 && !row.isEmpty()) {
                    cargarVersionEnEdicion(row.getItem());
                }
            });

            return row;
        });
    }

    /**
     * Carga todas las versiones asociadas a la marcaAsociada en la tabla.
     */
    private void cargarVersiones() {
        if (marcaAsociada == null) {
            log.warn("cargarVersiones(): marcaAsociada es null, no se puede cargar versiones.");
            return;
        }
        Disposable sub = vehiculoService.getVersionesByMarcaId(marcaAsociada.getId())
                .collectList()
                .subscribe(
                        lista -> Platform.runLater(() -> {
                            tvResultados.setItems(FXCollections.observableList(lista));
                            tvResultados.refresh();
                        }),
                        error -> Platform.runLater(() -> {
                            alertUtil.showError(error);
                            log.error("Error al cargar versiones de la marca {}: {}", marcaAsociada.getId(), error.getMessage());
                        })
                );
        registerSubscription(sub);
    }

    /**
     * Carga una versión en la UI para ser editada (modo edición).
     */
    private void cargarVersionEnEdicion(VehiculoVersion version) {
        this.versionEnEdicion = version;
        setModoEdicion();

        // p.ej. version="XLE"
        txtVersion.setText(version.getVersion());
        // descripción
        txtDescripcion.setText(version.getDescripcion() == null ? "" : version.getDescripcion());

        log.debug("Versión en edición: {}", version.getVersion());
    }

    /**
     * Pregunta confirmación antes de eliminar una versión.
     */
    private void onEliminarVersionClick(VehiculoVersion version) {
        Optional<ButtonType> result = alertUtil.showConfirmation(
                "Confirmar eliminación",
                "¿Desea eliminar esta versión?",
                "Versión: " + version.getVersion(),
                "Sí",
                "No"
        );
        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            eliminarVersion(version);
        }
    }

    /**
     * Elimina la versión en el servidor y recarga la tabla.
     */
    private void eliminarVersion(VehiculoVersion version) {
        Disposable sub = vehiculoService.deleteVehiculoVersion(version.getId())
                .subscribe(
                        // Mono<Void>: onNext => no hay
                        null,
                        error -> Platform.runLater(() -> {
                            alertUtil.showError(error);
                            log.error("Error al eliminar versión: {}", error.getMessage());
                        }),
                        () -> Platform.runLater(() -> {
                            log.info("Versión eliminada con éxito: {}", version);
                            alertUtil.showInfo("La versión \"" + version.getVersion() + "\" se eliminó correctamente.");
                            cargarVersiones();
                        })
                );
        registerSubscription(sub);
    }

    /**
     * Pasa a modo creación: txtVersion y txtDescripcion vacíos,
     * btnGuardar => "Crear",
     * título => "Creando Versión".
     */
    private void setModoCreacion() {
        versionEnEdicion = null;
        txtVersion.clear();
        txtDescripcion.clear();
        btnGuardar.setText("Crear");
        if (marcaAsociada != null) {
            Platform.runLater(() ->
                    setWindowTitle("Versiones de la marca: " + marcaAsociada.getMarca() + " - Creando Versión")
            );
        }
    }

    /**
     * Pasa a modo edición: btnGuardar => "Editar", título => "Editando Versión".
     */
    private void setModoEdicion() {
        btnGuardar.setText("Editar");
        if (marcaAsociada != null) {
            Platform.runLater(() ->
                    setWindowTitle("Versiones de la marca: " + marcaAsociada.getMarca() + " - Editando Versión")
            );
        }
    }

    /**
     * Cambia el título de la ventana, evitando NPE.
     */
    private void setWindowTitle(String newTitle) {
        Stage stage = (Stage) btnGuardar.getScene().getWindow();
        stage.setTitle(newTitle);
    }

    // =========================================================================
    //                           BUTTON HANDLERS
    // =========================================================================
    @FXML
    private void onBtnGuardarClick() {
        String versionIngresada = txtVersion.getText().trim();
        String descIngresada = txtDescripcion.getText() == null ? "" : txtDescripcion.getText().trim();

        if (versionIngresada.isEmpty()) {
            alertUtil.showError("Debe ingresar el nombre de la versión antes de guardar.");
            return;
        }
        if (marcaAsociada == null) {
            alertUtil.showError("No se ha definido la marca asociada.");
            return;
        }

        boolean esCreacion = (versionEnEdicion == null);

        if (esCreacion) {
            // --- MODO CREACIÓN ---
            VehiculoVersion nueva = new VehiculoVersion();
            nueva.setId(UUID.randomUUID());
            nueva.setVersion(versionIngresada);
            nueva.setDescripcion(descIngresada);

            // addVehiculoVersionToMarca => Mono<VehiculoMarca>
            Disposable sub = vehiculoService.addVehiculoVersionToMarca(marcaAsociada.getId(), nueva)
                    .subscribe(
                            vehiculoMarcaActualizada -> Platform.runLater(() -> {
                                log.info("Versión creada y agregada a la marca con éxito: {}", nueva);
                                alertUtil.showInfo("La versión \"" + versionIngresada + "\" fue creada correctamente.");
                                cargarVersiones();
                                setModoCreacion();
                            }),
                            error -> Platform.runLater(() -> {
                                alertUtil.showError(error);
                                log.error("Error al crear la versión: {}", error.getMessage());
                            })
                    );
            registerSubscription(sub);

        } else {
            // --- MODO EDICIÓN ---
            versionEnEdicion.setVersion(versionIngresada);
            versionEnEdicion.setDescripcion(descIngresada);

            // saveVehiculoVersion => Mono<VehiculoVersion>
            Disposable sub = vehiculoService.saveVehiculoVersion(versionEnEdicion)
                    .subscribe(
                            saved -> Platform.runLater(() -> {
                                log.info("Versión editada con éxito: {}", saved);
                                alertUtil.showInfo("La versión \"" + versionIngresada + "\" fue actualizada correctamente.");
                                cargarVersiones();
                                setModoCreacion();
                            }),
                            error -> Platform.runLater(() -> {
                                alertUtil.showError(error);
                                log.error("Error al actualizar la versión: {}", error.getMessage());
                            })
                    );
            registerSubscription(sub);
        }
    }

    @FXML
    private void onBtnLimpiarClick() {
        setModoCreacion();
    }

    @FXML
    private void onBtnSalirClick() {
        Stage stage = (Stage) btnSalir.getScene().getWindow();
        stage.close();
    }
}
