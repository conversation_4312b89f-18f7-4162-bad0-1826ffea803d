package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model;

import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class TipoDeMotor {
    private UUID id;

    private String tipoMotor;//gasolina, diesel-petr<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (Gasolina-Eléctrico), <PERSON><PERSON><PERSON><PERSON> (Diesel-Eléctrico), Eléctrico
    private Instant creadoActualizado;

    @Override
    public String toString() {
        return tipoMotor;
    }
}
