package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model;

import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import corp.jamaro.jamaroescritoriofx.appfx.model.file.ToBucketFileRelation;
import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class Vehiculo {
    private UUID id;
    private Set<VehiculoNombre> nombres;
    private VehiculoMarca vehiculoMarca;
    private VehiculoModelo vehiculoModelo;
    private Set<VehiculoAnio> vehiculoAnios;
    private VehiculoMotor vehiculoMotor;
    private VehiculoCilindrada vehiculoCilindrada;// (e.g., 1.5L, 2.0L).
    private VehiculoVersion vehiculoVersion;//(GL, XLE, Sport, basic, full, etc.).
    private VehiculoCarroceria vehiculoCarroceria;// (sedán, SUV, hatchback, camioneta, etc.).
    private VehiculoTraccion vehiculoTraccion;//Tracción delantera, trasera, 4x4, AWD, etc.
    private VehiculoTransmision vehiculoTransmision;//mecánica, automática, cvt, etc


    private Set<ToBucketFileRelation> files;

    private Instant creadoActualizado;

    private User user;

}
