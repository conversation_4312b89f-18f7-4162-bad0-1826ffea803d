package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model;

import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class VehiculoCarroceria {
    private UUID id;

    private String carroceria; // e.g., sedán, SUV, hatchback

    private String descripcion;
    private Instant creadoActualizado;

    @Override
    public String toString() {
        return carroceria;
    }
}
