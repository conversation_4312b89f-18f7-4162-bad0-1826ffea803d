package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model;

import lombok.Data;

import java.time.Instant;
import java.util.Set;
import java.util.UUID;

@Data
public class VehiculoMarca {
    private UUID id;
    private String marca; // Toyota, Nissan, etc.

    private Set<VehiculoModelo> modelos;
    private Set<VehiculoMotor> motores;
    private Set<VehiculoVersion> versiones;


    private Instant creadoActualizado;

    @Override
    public String toString() {
        return marca.toUpperCase();
    }
}
