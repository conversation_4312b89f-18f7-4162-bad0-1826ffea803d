package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model;

import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class VehiculoMotor {
    private UUID id;
    private String motor; // 4e, 5e, 3l
    private TipoDeMotor tipoDeMotor; // gasolina, diesel, híbrido, etc.

    private Instant creadoActualizado;

    @Override
    public String toString() {
        return motor.toUpperCase() + " - " + (tipoDeMotor != null ? tipoDeMotor.toString() : "Tipo de motor no especificado");
    }

}
