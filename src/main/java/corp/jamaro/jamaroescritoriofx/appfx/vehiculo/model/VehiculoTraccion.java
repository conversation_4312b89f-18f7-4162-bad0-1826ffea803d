package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model;

import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class VehiculoTraccion {
    private UUID id;

    private String tipoTraccion; // e.g., 4WD, FWD, RWD

    private String descripcion;
    private Instant creadoActualizado;

    @Override
    public String toString() {
        return tipoTraccion + " - " + descripcion;
    }
}
