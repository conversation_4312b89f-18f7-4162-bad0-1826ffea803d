package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model;

import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class VehiculoVersion {
    private UUID id;

    private String version; // GL, XLE, Sport, etc.
    private String descripcion;

    private Instant creadoActualizado;

    @Override
    public String toString() {
        return version + " - " + descripcion;
    }
}


/*
Un coche básico que se llama, por ejemplo, “Modelo X”. Ese mismo coche puede tener distintas presentaciones con más o menos accesorios.
Entonces el fabricante les da nombres o siglas específicas, como GL, XLE, Sport o similares, para mostrar cuál es la “receta” exacta de ese coche. Cada versión puede tener cosas distintas, por ejemplo:

GL: Podría ser la versión más básica, con características estándar.
XLE: Una versión más completa, con aire acondicionado automático, asientos de mejor calidad, o más tecnología en el tablero.
Sport: Una versión orientada a la deportividad, con un motor más potente, suspensión más firme o un diseño más agresivo.
 */