package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service;

import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.*;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Servicio (cliente) que se comunica con el servidor mediante RSocket,
 * gestionado por {@link ConnectionService}, para todas las operaciones
 * relacionadas con Vehiculo y sus subentidades, incluyendo el manejo
 * de borradores ("drafts").
 *
 * <p>Cada metodo llama a {@code connectionService.authenticatedRequest}
 * o {@code connectionService.authenticatedSubscription} para invocar
 * los endpoints RSocket publicados en el servidor.</p>
 */
@Slf4j
@Service
@AllArgsConstructor
public class VehiculoService {

    private final ConnectionService connectionService;

    // =========================================================================
    //                           VEHICULO
    // =========================================================================

    /**
     * Verifica si existe un Vehiculo con el ID proporcionado en el servidor.
     *
     * @param vehiculoId UUID del Vehiculo a verificar.
     * @return Mono<Boolean> que emite 'true' si existe, 'false' en caso contrario.
     */
    public Mono<Boolean> existsVehiculo(UUID vehiculoId) {
        log.info("Verificando si existe Vehiculo con ID={}", vehiculoId);
        return connectionService.authenticatedRequest(
                "vehiculo.exists",
                vehiculoId,
                Boolean.class
        );
    }


    /**
     * Obtiene todos los Vehiculos desde el servidor.
     */
    public Flux<Vehiculo> getAllVehiculos() {
        log.debug("Solicitando lista de todos los Vehiculos...");
        return connectionService.authenticatedSubscription(
                "vehiculo.getAll",
                null,
                Vehiculo.class
        );
    }

    /**
     * Crea o edita un Vehiculo en el servidor.
     */
    public Mono<Vehiculo> saveVehiculo(Vehiculo vehiculo) {
        log.info("Enviando solicitud para guardar Vehiculo con ID={}", vehiculo.getId());
        return connectionService.authenticatedRequest(
                "vehiculo.save",
                vehiculo,
                Vehiculo.class
        );
    }

    /**
     * Retorna el Vehiculo al cual pertenece un VehiculoNombre (buscando por UUID de VehiculoNombre).
     */
    public Mono<Vehiculo> getVehiculoByVehiculoNombreId(UUID vehiculoNombreId) {
        log.debug("Obteniendo Vehiculo relacionado al VehiculoNombreId={}", vehiculoNombreId);
        return connectionService.authenticatedRequest(
                "vehiculo.getByVehiculoNombre",
                vehiculoNombreId,
                Vehiculo.class
        );
    }

    /**
     * Elimina un Vehiculo por ID en el servidor.
     */
    public Mono<Void> deleteVehiculo(UUID vehiculoId) {
        log.warn("Eliminando Vehiculo con ID={}", vehiculoId);
        return connectionService.authenticatedRequest(
                "vehiculo.delete",
                vehiculoId,
                Void.class
        );
    }

    // =========================================================================
    //                     VEHICULO DRAFT (borrador)
    // =========================================================================

    /**
     * Solicita al servidor un borrador sin pasar ID (busca uno existente para el usuario,
     * o crea uno nuevo si no existe).
     */
    public Mono<Vehiculo> getDraftVehiculo() {
        log.debug("Solicitando borrador sin param (vehiculoDraft.get)");

        // En lugar de null, envía un String vacío (o cualquier objeto trivial).
        Object dataToSend = "";

        return connectionService.authenticatedRequest(
                "vehiculoDraft.get",
                dataToSend,
                Vehiculo.class
        );
    }


    /**
     * Solicita al servidor un borrador para un Vehiculo con ID dado.
     * Si el Vehiculo existe, lo clona; si no, crea un borrador vacío con ese ID.
     */
    public Mono<Vehiculo> getDraftVehiculoById(UUID vehiculoId) {
        log.debug("Solicitando borrador con ID={} (vehiculoDraft.getById)", vehiculoId);
        return connectionService.authenticatedRequest(
                "vehiculoDraft.getById",
                vehiculoId,
                Vehiculo.class
        );
    }

    /**
     * Guarda o actualiza el borrador en el servidor.
     * El cliente sigue usando la clase Vehiculo localmente.
     */
    public Mono<Vehiculo> saveVehiculoDraft(Vehiculo vehiculoDraft) {
        log.info("Guardando borrador de Vehiculo con ID={}",
                vehiculoDraft.getId());
        return connectionService.authenticatedRequest(
                "vehiculoDraft.save",
                vehiculoDraft,
                Vehiculo.class
        );
    }

    public Mono<Void> deleteVehiculoDraft(UUID draftId) {
        log.warn("Eliminando borrador de Vehiculo con ID={}", draftId);
        return connectionService.authenticatedRequest(
                "vehiculoDraft.delete",
                draftId,
                Void.class
        );
    }


    // =========================================================================
    //                         VEHICULO NOMBRE
    // =========================================================================

    public Mono<VehiculoNombre> saveVehiculoNombre(VehiculoNombre vehiculoNombre) {
        log.info("Guardando VehiculoNombre (nombre='{}')",
                vehiculoNombre != null ? vehiculoNombre.getNombre() : "null");
        return connectionService.authenticatedRequest(
                "vehiculoNombre.save",
                vehiculoNombre,
                VehiculoNombre.class
        );
    }

    public Flux<VehiculoNombre> getAllVehiculoNombres() {
        log.debug("Obteniendo todos los VehiculoNombre...");
        return connectionService.authenticatedSubscription(
                "vehiculoNombre.getAll",
                null,
                VehiculoNombre.class
        );
    }

    public Flux<VehiculoNombre> searchVehiculoNombre(String searchTerm) {
        log.debug("Buscando VehiculoNombre con el término='{}'", searchTerm);
        return connectionService.authenticatedSubscription(
                "vehiculoNombre.search",
                searchTerm,
                VehiculoNombre.class
        );
    }

    public Flux<VehiculoNombre> getVehiculoNombresByVehiculoId(UUID vehiculoId) {
        log.debug("Obteniendo nombres para Vehiculo con ID={}", vehiculoId);
        return connectionService.authenticatedSubscription(
                "vehiculoNombre.getByVehiculo",
                vehiculoId,
                VehiculoNombre.class
        );
    }

    public Mono<Void> deleteVehiculoNombre(UUID nombreId) {
        log.warn("Eliminando VehiculoNombre con ID={}", nombreId);
        return connectionService.authenticatedRequest(
                "vehiculoNombre.delete",
                nombreId,
                Void.class
        );
    }

    // =========================================================================
    //                         VEHICULO MARCA
    // =========================================================================

    public Flux<VehiculoMarca> getAllVehiculoMarcas() {
        log.debug("Solicitando lista de todas las Marcas de Vehiculo...");
        return connectionService.authenticatedSubscription(
                "vehiculoMarca.getAll",
                null,
                VehiculoMarca.class
        );
    }

    public Mono<VehiculoMarca> saveVehiculoMarca(VehiculoMarca marca) {
        log.info("Guardando VehiculoMarca con ID={}, marca='{}'",
                marca != null ? marca.getId() : null,
                marca != null ? marca.getMarca() : "null");
        return connectionService.authenticatedRequest(
                "vehiculoMarca.save",
                marca,
                VehiculoMarca.class
        );
    }

    public Mono<Void> deleteVehiculoMarca(UUID marcaId) {
        log.warn("Eliminando VehiculoMarca con ID={}", marcaId);
        return connectionService.authenticatedRequest(
                "vehiculoMarca.delete",
                marcaId,
                Void.class
        );
    }

    // =========================================================================
    //                         VEHICULO MODELO
    // =========================================================================

    public Mono<VehiculoModelo> saveVehiculoModelo(VehiculoModelo modelo) {
        log.info("Guardando VehiculoModelo con ID={}, modelo='{}'",
                modelo != null ? modelo.getId() : null,
                modelo != null ? modelo.getModelo() : "null");
        return connectionService.authenticatedRequest(
                "vehiculoModelo.save",
                modelo,
                VehiculoModelo.class
        );
    }

    public Mono<Void> deleteVehiculoModelo(UUID modeloId) {
        log.warn("Eliminando VehiculoModelo con ID={}", modeloId);
        return connectionService.authenticatedRequest(
                "vehiculoModelo.delete",
                modeloId,
                Void.class
        );
    }

    public Flux<VehiculoModelo> getModelosByMarcaId(UUID marcaId) {
        log.debug("Solicitando Modelos para la Marca con ID={}", marcaId);
        return connectionService.authenticatedSubscription(
                "vehiculoModelo.getByMarca",
                marcaId,
                VehiculoModelo.class
        );
    }

    public Mono<VehiculoMarca> addVehiculoModeloToMarca(UUID marcaId, VehiculoModelo vehiculoModelo) {
        log.info("Asociando Modelo (ID={}) a Marca (ID={})",
                vehiculoModelo != null ? vehiculoModelo.getId() : null,
                marcaId);
        VehiculoModeloRequest request = new VehiculoModeloRequest(marcaId, vehiculoModelo);
        return connectionService.authenticatedRequest(
                "vehiculoModelo.addToMarca",
                request,
                VehiculoMarca.class
        );
    }

    // =========================================================================
    //                         VEHICULO MOTOR
    // =========================================================================

    public Mono<VehiculoMotor> saveVehiculoMotor(VehiculoMotor motor) {
        log.info("Guardando VehiculoMotor con ID={}, motor='{}'",
                motor != null ? motor.getId() : null,
                motor != null ? motor.getMotor() : "null");
        return connectionService.authenticatedRequest(
                "vehiculoMotor.save",
                motor,
                VehiculoMotor.class
        );
    }

    public Mono<Void> deleteVehiculoMotor(UUID motorId) {
        log.warn("Eliminando VehiculoMotor con ID={}", motorId);
        return connectionService.authenticatedRequest(
                "vehiculoMotor.delete",
                motorId,
                Void.class
        );
    }

    public Flux<VehiculoMotor> getMotoresByMarcaId(UUID marcaId) {
        log.debug("Obteniendo motores para la Marca con ID={}", marcaId);
        return connectionService.authenticatedSubscription(
                "vehiculoMotor.getByMarca",
                marcaId,
                VehiculoMotor.class
        );
    }

    public Mono<VehiculoMarca> addVehiculoMotorToMarca(UUID marcaId, VehiculoMotor vehiculoMotor) {
        log.info("Asociando Motor (ID={}) a Marca (ID={})",
                vehiculoMotor != null ? vehiculoMotor.getId() : null,
                marcaId);
        VehiculoMotorRequest request = new VehiculoMotorRequest(marcaId, vehiculoMotor);
        return connectionService.authenticatedRequest(
                "vehiculoMotor.addToMarca",
                request,
                VehiculoMarca.class
        );
    }

    // =========================================================================
    //                         TIPO DE MOTOR
    // =========================================================================

    public Flux<TipoDeMotor> getAllTipoDeMotor() {
        log.debug("Solicitando lista de todos los Tipos de Motor...");
        return connectionService.authenticatedSubscription(
                "tipoDeMotor.getAll",
                null,
                TipoDeMotor.class
        );
    }

    // =========================================================================
    //                         VEHICULO CILINDRADA
    // =========================================================================

    public Flux<VehiculoCilindrada> getAllVehiculoCilindradas() {
        log.debug("Solicitando lista de todas las Cilindradas de Vehiculo...");
        return connectionService.authenticatedSubscription(
                "vehiculoCilindrada.getAll",
                null,
                VehiculoCilindrada.class
        );
    }

    // =========================================================================
    //                         VEHICULO VERSION
    // =========================================================================

    public Mono<VehiculoVersion> saveVehiculoVersion(VehiculoVersion version) {
        log.info("Guardando VehiculoVersion con ID={}, version='{}'",
                version != null ? version.getId() : null,
                version != null ? version.getVersion() : "null");
        return connectionService.authenticatedRequest(
                "vehiculoVersion.save",
                version,
                VehiculoVersion.class
        );
    }

    public Mono<Void> deleteVehiculoVersion(UUID versionId) {
        log.warn("Eliminando VehiculoVersion con ID={}", versionId);
        return connectionService.authenticatedRequest(
                "vehiculoVersion.delete",
                versionId,
                Void.class
        );
    }

    public Flux<VehiculoVersion> getVersionesByMarcaId(UUID marcaId) {
        log.debug("Obteniendo versiones para la Marca con ID={}", marcaId);
        return connectionService.authenticatedSubscription(
                "vehiculoVersion.getByMarca",
                marcaId,
                VehiculoVersion.class
        );
    }

    public Mono<VehiculoMarca> addVehiculoVersionToMarca(UUID marcaId, VehiculoVersion vehiculoVersion) {
        log.info("Asociando Version (ID={}) a Marca (ID={})",
                vehiculoVersion != null ? vehiculoVersion.getId() : null,
                marcaId);
        VehiculoVersionRequest request = new VehiculoVersionRequest(marcaId, vehiculoVersion);
        return connectionService.authenticatedRequest(
                "vehiculoVersion.addToMarca",
                request,
                VehiculoMarca.class
        );
    }

    // =========================================================================
    //                         VEHICULO CARROCERIA
    // =========================================================================

    public Flux<VehiculoCarroceria> getAllVehiculoCarrocerias() {
        log.debug("Solicitando lista de todas las Carrocerías de Vehiculo...");
        return connectionService.authenticatedSubscription(
                "vehiculoCarroceria.getAll",
                null,
                VehiculoCarroceria.class
        );
    }

    // =========================================================================
    //                         VEHICULO TRACCION
    // =========================================================================

    public Flux<VehiculoTraccion> getAllVehiculoTracciones() {
        log.debug("Solicitando lista de todas las Tracciones de Vehiculo...");
        return connectionService.authenticatedSubscription(
                "vehiculoTraccion.getAll",
                null,
                VehiculoTraccion.class
        );
    }

    // =========================================================================
    //                         VEHICULO TRANSMISION
    // =========================================================================

    public Flux<VehiculoTransmision> getAllVehiculoTransmisiones() {
        log.debug("Solicitando lista de todas las Transmisiones de Vehiculo...");
        return connectionService.authenticatedSubscription(
                "vehiculoTransmision.getAll",
                null,
                VehiculoTransmision.class
        );
    }

    // =========================================================================
    //               Clases Request auxiliares (cliente)
    // =========================================================================
    // Coinciden con las estructuras que el servidor usa en su RSocket Controller.

    @Data
    @AllArgsConstructor
    public static class VehiculoModeloRequest {
        private UUID marcaId;
        private VehiculoModelo vehiculoModelo;
    }

    @Data
    @AllArgsConstructor
    public static class VehiculoMotorRequest {
        private UUID marcaId;
        private VehiculoMotor vehiculoMotor;
    }

    @Data
    @AllArgsConstructor
    public static class VehiculoVersionRequest {
        private UUID marcaId;
        private VehiculoVersion vehiculoVersion;
    }
}
