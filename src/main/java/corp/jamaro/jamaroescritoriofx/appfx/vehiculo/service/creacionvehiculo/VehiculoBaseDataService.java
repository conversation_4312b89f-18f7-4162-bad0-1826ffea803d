package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.creacionvehiculo;

import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.*;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.VehiculoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.UUID;
import java.util.function.Consumer;

/**
 * Servicio para cargar datos base relacionados con Vehiculos.
 * Se apoya en VehiculoService para obtener la información,
 * pero expone métodos más pequeños y reutilizables.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VehiculoBaseDataService {

    private final VehiculoService vehiculoService;

    /**
     * Carga todas las marcas.
     */
    public void loadMarcas(Consumer<List<VehiculoMarca>> onSuccess, Consumer<Throwable> onError) {
        subscribeFlux(vehiculoService.getAllVehiculoMarcas(), onSuccess, onError);
    }

    /**
     * Carga los modelos de una marca dada.
     */
    public void loadModelosByMarca(UUID marcaId, Consumer<List<VehiculoModelo>> onSuccess, Consumer<Throwable> onError) {
        subscribeFlux(vehiculoService.getModelosByMarcaId(marcaId), onSuccess, onError);
    }

    /**
     * Carga los motores de una marca dada.
     */
    public void loadMotoresByMarca(UUID marcaId, Consumer<List<VehiculoMotor>> onSuccess, Consumer<Throwable> onError) {
        subscribeFlux(vehiculoService.getMotoresByMarcaId(marcaId), onSuccess, onError);
    }

    /**
     * Carga las versiones de una marca dada.
     */
    public void loadVersionesByMarca(UUID marcaId, Consumer<List<VehiculoVersion>> onSuccess, Consumer<Throwable> onError) {
        subscribeFlux(vehiculoService.getVersionesByMarcaId(marcaId), onSuccess, onError);
    }

    /**
     * Carga todas las cilindradas.
     */
    public void loadCilindradas(Consumer<List<VehiculoCilindrada>> onSuccess, Consumer<Throwable> onError) {
        subscribeFlux(vehiculoService.getAllVehiculoCilindradas(), onSuccess, onError);
    }

    /**
     * Carga todas las carrocerías.
     */
    public void loadCarrocerias(Consumer<List<VehiculoCarroceria>> onSuccess, Consumer<Throwable> onError) {
        subscribeFlux(vehiculoService.getAllVehiculoCarrocerias(), onSuccess, onError);
    }

    /**
     * Carga todas las tracciones.
     */
    public void loadTracciones(Consumer<List<VehiculoTraccion>> onSuccess, Consumer<Throwable> onError) {
        subscribeFlux(vehiculoService.getAllVehiculoTracciones(), onSuccess, onError);
    }

    /**
     * Carga todas las transmisiones.
     */
    public void loadTransmisiones(Consumer<List<VehiculoTransmision>> onSuccess, Consumer<Throwable> onError) {
        subscribeFlux(vehiculoService.getAllVehiculoTransmisiones(), onSuccess, onError);
    }

    /**
     * Metodo genérico para suscribir un Flux<T> y recolectarlo en una lista.
     */
    private <T> void subscribeFlux(Flux<T> flux,
                                   Consumer<List<T>> onSuccess,
                                   Consumer<Throwable> onError) {
        flux.collectList()
                .subscribe(
                        onSuccess,
                        error -> {
                            log.error("Error al cargar datos base: {}", error.getMessage());
                            onError.accept(error);
                        }
                );
    }
}
