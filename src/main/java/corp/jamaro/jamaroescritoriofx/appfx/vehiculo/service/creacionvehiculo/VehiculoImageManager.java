package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.creacionvehiculo;

import corp.jamaro.jamaroescritoriofx.appfx.model.file.BucketFile;
import corp.jamaro.jamaroescritoriofx.appfx.model.file.ToBucketFileRelation;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.viewmodel.VehiculoViewModel;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Componente para manejar la lógica de agregación, eliminación y
 * navegación (siguiente/anterior) de imágenes en el VehiculoViewModel.
 */
@Slf4j
@Component
public class VehiculoImageManager {

    private int currentIndex = 0;

    /**
     * Agrega una nueva imagen (url) al ViewModel. Si es la primera,
     * setea currentIndex a 0.
     */
    public void addImage(VehiculoViewModel viewModel, String url) {
        var nuevaRel = new ToBucketFileRelation();
        nuevaRel.setId(UUID.randomUUID().toString());
        nuevaRel.setOrden(viewModel.getImagenesObservable().size() + 1);
        nuevaRel.setFileType("imagen");
        nuevaRel.setIsThumbnail(false);

        var bucketFile = new BucketFile();
        bucketFile.setId(UUID.randomUUID());
        // Crear un objectName basado en la URL
        String objectName = url.substring(url.lastIndexOf('/') + 1);
        bucketFile.setObjectName(objectName);
        bucketFile.setContentType(getContentTypeFromUrl(url));
        bucketFile.setCreadoActualizado(Instant.now());

        // Agregar metadatos para la URL
        Map<String, String> metadata = new HashMap<>();
        metadata.put("url", url);
        bucketFile.setMetadata(metadata);

        nuevaRel.setBucketFile(bucketFile);
        viewModel.addFile(nuevaRel);

        log.info("Archivo agregado: {}. Total archivos: {}",
                url, viewModel.getImagenesObservable().size());

        // Si es el primer archivo, mostrarlo de inmediato
        if (viewModel.getImagenesObservable().size() == 1) {
            currentIndex = 0;
        }
    }

    /**
     * Determina el tipo de contenido basado en la extensión de la URL
     */
    private String getContentTypeFromUrl(String url) {
        if (url.toLowerCase().endsWith(".jpg") || url.toLowerCase().endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (url.toLowerCase().endsWith(".png")) {
            return "image/png";
        } else if (url.toLowerCase().endsWith(".gif")) {
            return "image/gif";
        } else if (url.toLowerCase().endsWith(".webp")) {
            return "image/webp";
        } else {
            return "image/jpeg"; // Default
        }
    }

    /**
     * Elimina la imagen actual.
     */
    public void deleteCurrentImage(VehiculoViewModel viewModel) {
        if (viewModel.getImagenesObservable().isEmpty()) {
            return;
        }
        var actual = viewModel.getImagenesObservable().get(currentIndex);
        viewModel.removeFile(actual);

        if (currentIndex >= viewModel.getImagenesObservable().size()) {
            currentIndex = viewModel.getImagenesObservable().size() - 1;
        }
    }

    /**
     * Navega a la imagen anterior.
     */
    public void goBackImage(VehiculoViewModel viewModel) {
        if (viewModel.getImagenesObservable().isEmpty()) {
            return;
        }
        currentIndex = (currentIndex - 1 + viewModel.getImagenesObservable().size())
                % viewModel.getImagenesObservable().size();
    }

    /**
     * Navega a la imagen siguiente.
     */
    public void goNextImage(VehiculoViewModel viewModel) {
        if (viewModel.getImagenesObservable().isEmpty()) {
            return;
        }
        currentIndex = (currentIndex + 1) % viewModel.getImagenesObservable().size();
    }

    /**
     * Metodo de ayuda para actualizar el ImageView con la imagen correspondiente al currentIndex.
     */
    public void updateImageView(VehiculoViewModel viewModel, ImageView imgView) {
        var files = viewModel.getImagenesObservable();
        if (files.isEmpty()) {
            imgView.setImage(null);
            return;
        }
        var rel = files.get(currentIndex);
        String url = "";

        // Obtener la URL desde los metadatos del BucketFile
        if (rel.getBucketFile() != null && rel.getBucketFile().getMetadata() != null) {
            url = rel.getBucketFile().getMetadata().get("url");
        }

        if (url != null && !url.isBlank()) {
            imgView.setImage(new Image(url, true));
        } else {
            imgView.setImage(null);
        }
    }

    public int getCurrentIndex() {
        return currentIndex;
    }
}
