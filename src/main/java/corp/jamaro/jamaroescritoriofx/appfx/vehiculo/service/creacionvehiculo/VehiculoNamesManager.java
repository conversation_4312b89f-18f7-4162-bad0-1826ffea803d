package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.creacionvehiculo;

import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoNombre;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.viewmodel.VehiculoViewModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.UUID;

/**
 * Clase auxiliar para manejar la lógica de Nombres Adicionales de Vehiculo:
 * Agregar, eliminar y reordenar (drag & drop).
 */
@Slf4j
@Component
public class VehiculoNamesManager {

    /**
     * Agrega un nuevo nombre adicional al ViewModel.
     * Ajusta su creadoActualizado para que no colisione con el principal.
     */
    public void addNombreAdicional(VehiculoViewModel viewModel, String nombre) {
        if (nombre == null || nombre.isBlank()) {
            return;
        }
        var nuevoNombre = new VehiculoNombre();
        nuevoNombre.setId(UUID.randomUUID());
        nuevoNombre.setNombre(nombre);
        nuevoNombre.setCreadoEl(Instant.now());


        // El ViewModel ya lleva la lógica de "addNombreAdicional(...)"
        viewModel.addNombreAdicional(nuevoNombre);

        log.info("Nombre adicional agregado: {}", nombre);
    }

    /**
     * Elimina un nombre adicional del ViewModel.
     */
    public void removeNombreAdicional(VehiculoViewModel viewModel, VehiculoNombre nombre) {
        if (nombre == null) {
            return;
        }
        viewModel.removeNombreAdicional(nombre);
        log.info("Nombre adicional eliminado: {}", nombre.getNombre());
    }

    /**
     * Reordena la lista de nombres para reflejar drag & drop.
     */
    public void reorderNombreAdicional(VehiculoViewModel viewModel,
                                       VehiculoNombre draggedItem,
                                       VehiculoNombre dropTarget) {
        if (draggedItem == null || dropTarget == null) {
            return;
        }
        viewModel.reorderNombreAdicional(draggedItem, dropTarget);
        log.info("Reordenando nombre: {} => {}", draggedItem.getNombre(), dropTarget.getNombre());
    }
}
