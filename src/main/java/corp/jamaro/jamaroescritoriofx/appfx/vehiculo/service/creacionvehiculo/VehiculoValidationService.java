package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.creacionvehiculo;

import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Servicio de validación para datos relacionados con Vehiculo.
 * Aquí se concentra la lógica de validaciones de los años.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VehiculoValidationService {

    private final AlertUtil alertUtil;

    /**
     * Valida que el año de inicio sea:
     *  1) Un número válido
     *  2) Mayor que 1900
     *
     * @param anioInicio valor de txtAnioInicio
     * @return true si está OK, false si hay error.
     */
    public boolean validateAnioInicio(String anioInicio) {
        if (anioInicio == null || anioInicio.isBlank()) {
            alertUtil.showError("Debe ingresar un año de inicio. No puede estar vacío.");
            return false;
        }

        try {
            int iInicio = Integer.parseInt(anioInicio.trim());
            if (iInicio <= 1900) {
                alertUtil.showError("El año inicial debe ser mayor que 1900.");
                return false;
            }
            return true;
        } catch (NumberFormatException e) {
            alertUtil.showError("Año inicial inválido. Asegúrate de ingresar solo dígitos.");
            log.error("Error parseando año de inicio: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Valida que el año final sea:
     *  1) Un número válido (si está en blanco, se asume igual al de inicio)
     *  2) Mayor que el año inicial
     *  3) Que la diferencia no exceda 15 años
     *
     * @param anioInicio valor de txtAnioInicio (ya validado antes)
     * @param anioFinal  valor de txtAnioFinal
     * @return true si está OK, false si hay error.
     */
    public boolean validateAnioFinal(String anioInicio, String anioFinal) {
        if (anioFinal == null || anioFinal.isBlank()) {
            alertUtil.showError("Debe ingresar un año final. No puede estar vacío.");
            return false;
        }

        try {
            int iInicio = Integer.parseInt(anioInicio.trim());
            // Si no hay año final, asumimos el inicio
            int iFin = (anioFinal == null || anioFinal.isBlank())
                    ? iInicio
                    : Integer.parseInt(anioFinal.trim());

            // Validar que el año final sea mayor que el inicial
            if (iFin <= iInicio) {
                alertUtil.showError("El año final debe ser mayor que el año inicial.");
                return false;
            }
            // Validar que la diferencia no exceda 15 años
            if ((iFin - iInicio) > 15) {
                alertUtil.showError("La diferencia entre el año inicial y el final no puede superar los 15 años.");
                return false;
            }
            return true;

        } catch (NumberFormatException e) {
            alertUtil.showError("Año final inválido. Asegúrate de ingresar solo dígitos.");
            log.error("Error parseando año final: {}", e.getMessage());
            return false;
        }
    }
}
