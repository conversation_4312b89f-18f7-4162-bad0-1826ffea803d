package corp.jamaro.jamaroescritoriofx.appfx.vehiculo.viewmodel;

import corp.jamaro.jamaroescritoriofx.appfx.model.file.ToBucketFileRelation;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.*;
import javafx.beans.property.*;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.*;

/**
 * ViewModel que representa el estado y lógica de edición/creación de un Vehiculo.
 * Permite bindear propiedades JavaFX directamente, simplificando el Controller.
 */
@Slf4j
public class VehiculoViewModel {

    // ------------------------------------------------------------------------
    // PROPIEDADES PRINCIPALES
    // ------------------------------------------------------------------------
    /** Vehiculo “en edición” o “en creación”. */
    private final ObjectProperty<Vehiculo> vehiculoProperty = new SimpleObjectProperty<>();

    /** Marca, Modelo, etc. */
    private final ObjectProperty<VehiculoMarca>       marcaProperty         = new SimpleObjectProperty<>();
    private final ObjectProperty<VehiculoModelo>      modeloProperty        = new SimpleObjectProperty<>();
    private final StringProperty                      anioInicioProperty    = new SimpleStringProperty();
    private final StringProperty                      anioFinalProperty     = new SimpleStringProperty();
    private final ObjectProperty<VehiculoMotor>       motorProperty         = new SimpleObjectProperty<>();
    private final ObjectProperty<VehiculoCilindrada>  cilindradaProperty    = new SimpleObjectProperty<>();
    private final ObjectProperty<VehiculoVersion>     versionProperty       = new SimpleObjectProperty<>();
    private final ObjectProperty<VehiculoCarroceria>  carroceriaProperty    = new SimpleObjectProperty<>();
    private final ObjectProperty<VehiculoTraccion>    traccionProperty      = new SimpleObjectProperty<>();
    private final ObjectProperty<VehiculoTransmision> transmisionProperty   = new SimpleObjectProperty<>();

    /**
     * Nombre principal que se muestra en txtNombreVehiculo.
     * (El “principal” es el más antiguo de la lista).
     */
    private final StringProperty nombrePrincipalProperty = new SimpleStringProperty();

    /**
     * Lista de nombres “adicionales” para el ListView.
     */
    @Getter
    private final ObservableList<VehiculoNombre> nombresAdicionales = FXCollections.observableArrayList();

    /**
     * Lista (observable) de archivos para poder mostrarlos, navegarlos, etc.
     */
    @Getter
    private final ObservableList<ToBucketFileRelation> filesObservable = FXCollections.observableArrayList();

    // ------------------------------------------------------------------------
    // CONSTRUCTORES
    // ------------------------------------------------------------------------
    /**
     * Constructor por defecto: crea un nuevo Vehiculo (modo creación).
     */
    public VehiculoViewModel() {
        initVehiculo(null);
        setupBindings();
    }

    /**
     * Constructor que recibe un Vehiculo existente (modo edición).
     */
    public VehiculoViewModel(Vehiculo vehiculoExistente) {
        initVehiculo(vehiculoExistente);
        setupBindings();
    }

    // ------------------------------------------------------------------------
    // MÉTODOS PRINCIPALES
    // ------------------------------------------------------------------------
    /**
     * Inicializa (o reinicializa) el vehiculoProperty y sincroniza los campos individuales.
     * @param vehiculo Puede ser null para modo creación.
     */
    public final void initVehiculo(Vehiculo vehiculo) {
        if (vehiculo == null) {
            // MODO CREACIÓN
            var nuevo = new Vehiculo();
            nuevo.setId(UUID.randomUUID());

            // Primer nombre principal en blanco
            var nombrePpal = new VehiculoNombre();
            nombrePpal.setId(UUID.randomUUID());
            nombrePpal.setCreadoEl(Instant.now());
            nombrePpal.setNombre("");
            nuevo.setNombres(Set.of(nombrePpal)); // Set.of => set inmutable, pero lo usaremos para inicializar

            this.vehiculoProperty.set(nuevo);
        } else {
            // MODO EDICIÓN
            this.vehiculoProperty.set(vehiculo);
        }
        loadFromVehiculo();
    }

    /**
     * Retorna el Vehiculo “construido” con los valores de las propiedades,
     * útil al momento de guardar.
     */
    public Vehiculo getVehiculo() {
        saveToVehiculo();
        return vehiculoProperty.get();
    }

    // ------------------------------------------------------------------------
    // CARGA DE DATOS DESDE/AL VEHICULO
    // ------------------------------------------------------------------------
    /**
     * Carga los valores de un Vehiculo en las propiedades del ViewModel.
     */
    private void loadFromVehiculo() {
        var v = vehiculoProperty.get();
        if (v == null) return;

        // Marca/Modelo
        marcaProperty.set(v.getVehiculoMarca());
        modeloProperty.set(v.getVehiculoModelo());

        // Años -> tomamos el mínimo y el máximo
        if (v.getVehiculoAnios() != null && !v.getVehiculoAnios().isEmpty()) {
            int min = v.getVehiculoAnios().stream().mapToInt(VehiculoAnio::getYear).min().orElse(0);
            int max = v.getVehiculoAnios().stream().mapToInt(VehiculoAnio::getYear).max().orElse(0);
            anioInicioProperty.set(min == 0 ? "" : String.valueOf(min));
            anioFinalProperty.set(max == 0 ? "" : String.valueOf(max));
        } else {
            anioInicioProperty.set("");
            anioFinalProperty.set("");
        }

        // Motor, cilindrada, versión, carrocería, etc.
        motorProperty.set(v.getVehiculoMotor());
        cilindradaProperty.set(v.getVehiculoCilindrada());
        versionProperty.set(v.getVehiculoVersion());
        carroceriaProperty.set(v.getVehiculoCarroceria());
        traccionProperty.set(v.getVehiculoTraccion());
        transmisionProperty.set(v.getVehiculoTransmision());

        // NOMBRES (principal + adicionales)
        nombresAdicionales.clear();
        if (v.getNombres() != null && !v.getNombres().isEmpty()) {
            var ordenados = v.getNombres().stream()
                    .sorted(Comparator.comparing(VehiculoNombre::getCreadoEl))
                    .toList();

            // El primero es el principal
            var ppal = ordenados.get(0);
            nombrePrincipalProperty.set(ppal.getNombre());

            // El resto, adicionales
            if (ordenados.size() > 1) {
                nombresAdicionales.addAll(ordenados.subList(1, ordenados.size()));
            }
        } else {
            nombrePrincipalProperty.set("");
        }

        // ARCHIVOS
        filesObservable.clear();
        if (v.getFiles() != null && !v.getFiles().isEmpty()) {
            filesObservable.addAll(v.getFiles());
        }
    }

    /**
     * Sincroniza las propiedades del ViewModel de vuelta hacia el objeto Vehiculo.
     */
    private void saveToVehiculo() {
        var v = vehiculoProperty.get();
        if (v == null) return;

        // Marca/Modelo
        v.setVehiculoMarca(marcaProperty.get());
        v.setVehiculoModelo(modeloProperty.get());

        // Años
        var anios = parseYearRange(anioInicioProperty.get(), anioFinalProperty.get());
        v.setVehiculoAnios(anios.isEmpty() ? null : anios);

        // Motor, cilindrada, versión, etc.
        v.setVehiculoMotor(motorProperty.get());
        v.setVehiculoCilindrada(cilindradaProperty.get());
        v.setVehiculoVersion(versionProperty.get());
        v.setVehiculoCarroceria(carroceriaProperty.get());
        v.setVehiculoTraccion(traccionProperty.get());
        v.setVehiculoTransmision(transmisionProperty.get());

        // NOMBRES => principal + adicionales
        var todosLosNombres = new ArrayList<VehiculoNombre>();
        if (v.getNombres() != null && !v.getNombres().isEmpty()) {
            // Tomamos el principal existente (el más antiguo)
            var sorted = v.getNombres().stream()
                    .sorted(Comparator.comparing(VehiculoNombre::getCreadoEl))
                    .toList();
            var principal = sorted.get(0);
            principal.setNombre(nombrePrincipalProperty.get());
            todosLosNombres.add(principal);
        } else {
            // Crear uno nuevo si no existía
            var principal = new VehiculoNombre();
            principal.setId(UUID.randomUUID());
            principal.setCreadoEl(Instant.now());
            principal.setNombre(nombrePrincipalProperty.get());
            todosLosNombres.add(principal);
        }
        // Añadir adicionales
        todosLosNombres.addAll(nombresAdicionales);

        // Reconstruir el set de nombres
        v.setNombres(new HashSet<>(todosLosNombres));

        // ARCHIVOS
        if (filesObservable.isEmpty()) {
            v.setFiles(null);
        } else {
            // LinkedHashSet para preservar el orden en que están en la lista
            v.setFiles(new LinkedHashSet<>(filesObservable));
        }
    }

    /**
     * Parsea los años de inicio y fin, retornando un set de VehiculoAnio.
     * - Si hay anioInicio y no hay anioFin => crea solo ese año.
     * - Si hay ambos => crea el rango [inicio..fin].
     * - Si anioInicio está vacío o falla => set vacío.
     */
    private Set<VehiculoAnio> parseYearRange(String anioInicio, String anioFin) {
        var anios = new HashSet<VehiculoAnio>();
        if (anioInicio == null || anioInicio.isBlank()) return anios;

        try {
            var start = Integer.parseInt(anioInicio.trim());
            if (anioFin == null || anioFin.isBlank()) {
                // Solo un año
                var va = new VehiculoAnio();
                va.setYear(start);
                anios.add(va);
            } else {
                // Rango
                var end = Integer.parseInt(anioFin.trim());
                for (int y = start; y <= end; y++) {
                    var va = new VehiculoAnio();
                    va.setYear(y);
                    anios.add(va);
                }
            }
        } catch (NumberFormatException ignored) {
            // set vacío
        }
        return anios;
    }

    // ------------------------------------------------------------------------
    // BINDINGS Y ACTUALIZACIÓN DINÁMICA
    // ------------------------------------------------------------------------
    /**
     * Configura los listeners para actualizar el nombre principal en función de ciertos cambios.
     */
    private void setupBindings() {
        // Cada vez que cambia uno de estos, recalculamos el nombre principal.
        marcaProperty.addListener((obs, o, n) -> updateNombrePrincipal());
        modeloProperty.addListener((obs, o, n) -> updateNombrePrincipal());
        anioInicioProperty.addListener((obs, o, n) -> updateNombrePrincipal());
        anioFinalProperty.addListener((obs, o, n) -> updateNombrePrincipal());
        motorProperty.addListener((obs, o, n) -> updateNombrePrincipal());
        cilindradaProperty.addListener((obs, o, n) -> updateNombrePrincipal());
        versionProperty.addListener((obs, o, n) -> updateNombrePrincipal());
        carroceriaProperty.addListener((obs, o, n) -> updateNombrePrincipal());
        traccionProperty.addListener((obs, o, n) -> updateNombrePrincipal());
        transmisionProperty.addListener((obs, o, n) -> updateNombrePrincipal());
    }

    /**
     * Actualiza "nombrePrincipalProperty" concatenando varios campos para
     * tener una referencia rápida al nombre del vehículo.
     */
    private void updateNombrePrincipal() {
        var sb = new StringBuilder();

        if (modeloProperty.get() != null) {
            sb.append(modeloProperty.get().getModelo());
        }
        if (!anioInicioProperty.get().isBlank()) {
            sb.append(" ").append(anioInicioProperty.get());
        }
        if (!anioFinalProperty.get().isBlank()) {
            sb.append("-").append(anioFinalProperty.get());
        }
        if (motorProperty.get() != null) {
            sb.append(" ").append(motorProperty.get().getMotor());
        }
        if (cilindradaProperty.get() != null) {
            sb.append(" ").append(cilindradaProperty.get().getCilindrada());
        }
        if (versionProperty.get() != null) {
            sb.append(" ").append(versionProperty.get().getVersion());
        }
        if (carroceriaProperty.get() != null) {
            sb.append(" ").append(carroceriaProperty.get().getCarroceria());
        }
        if (traccionProperty.get() != null) {
            sb.append(" ").append(traccionProperty.get().getTipoTraccion());
        }
        if (transmisionProperty.get() != null) {
            sb.append(" ").append(transmisionProperty.get().getTransmision());
        }
        nombrePrincipalProperty.set(sb.toString().trim());
    }

    // ------------------------------------------------------------------------
    // GETTERS DE LAS PROPIEDADES
    // ------------------------------------------------------------------------
    public ObjectProperty<Vehiculo> vehiculoProperty()             { return vehiculoProperty; }
    public ObjectProperty<VehiculoMarca> marcaProperty()           { return marcaProperty; }
    public ObjectProperty<VehiculoModelo> modeloProperty()         { return modeloProperty; }
    public StringProperty anioInicioProperty()                     { return anioInicioProperty; }
    public StringProperty anioFinalProperty()                      { return anioFinalProperty; }
    public ObjectProperty<VehiculoMotor> motorProperty()           { return motorProperty; }
    public ObjectProperty<VehiculoCilindrada> cilindradaProperty() { return cilindradaProperty; }
    public ObjectProperty<VehiculoVersion> versionProperty()       { return versionProperty; }
    public ObjectProperty<VehiculoCarroceria> carroceriaProperty() { return carroceriaProperty; }
    public ObjectProperty<VehiculoTraccion> traccionProperty()     { return traccionProperty; }
    public ObjectProperty<VehiculoTransmision> transmisionProperty(){ return transmisionProperty; }
    public StringProperty nombrePrincipalProperty()                { return nombrePrincipalProperty; }

    // ------------------------------------------------------------------------
    // MANEJO DE NOMBRES ADICIONALES
    // ------------------------------------------------------------------------
    /**
     * Agrega un nuevo VehiculoNombre a la lista de nombresAdicionales.
     */
    public void addNombreAdicional(VehiculoNombre nombre) {
        if (nombre != null) {
            // Ajustar su createdEl para no colisionar con principal
            nombre.setCreadoEl(Instant.now().plusSeconds(nombresAdicionales.size() + 1));
            nombresAdicionales.add(nombre);
        }
    }

    /**
     * Elimina un nombre de la lista adicional.
     */
    public void removeNombreAdicional(VehiculoNombre nombre) {
        nombresAdicionales.remove(nombre);
    }

    /**
     * Reordena la lista de nombresAdicionales cambiando su createdEl
     * para reflejar el nuevo orden.
     */
    public void reorderNombreAdicional(VehiculoNombre draggedItem, VehiculoNombre dropTarget) {
        if (draggedItem == null || dropTarget == null || draggedItem == dropTarget) return;

        var fromIndex = nombresAdicionales.indexOf(draggedItem);
        var toIndex   = nombresAdicionales.indexOf(dropTarget);
        if (fromIndex < 0 || toIndex < 0) return;

        // Mover
        nombresAdicionales.remove(draggedItem);
        nombresAdicionales.add(toIndex, draggedItem);

        // Reasignar createdEl para reflejar el nuevo orden
        var baseTime = Instant.now().minusSeconds(nombresAdicionales.size() * 2L);
        for (int i = 0; i < nombresAdicionales.size(); i++) {
            var vn = nombresAdicionales.get(i);
            vn.setCreadoEl(baseTime.plusSeconds(i));
        }
    }

    // ------------------------------------------------------------------------
    // MANEJO DE ARCHIVOS (AGREGAR/REMOVER/REORDENAR)
    // ------------------------------------------------------------------------
    /**
     * Agrega un nuevo archivo a la lista de archivos.
     */
    public void addFile(ToBucketFileRelation nuevoArchivo) {
        if (nuevoArchivo != null) {
            filesObservable.add(nuevoArchivo);
        }
    }

    /**
     * Elimina un archivo de la lista.
     */
    public void removeFile(ToBucketFileRelation archivo) {
        filesObservable.remove(archivo);
    }

    /**
     * Reordena la lista de archivos y recalcula su orden.
     */
    public void reorderFile(ToBucketFileRelation draggedItem, ToBucketFileRelation dropTarget) {
        if (draggedItem == null || dropTarget == null || draggedItem == dropTarget) return;

        var fromIndex = filesObservable.indexOf(draggedItem);
        var toIndex   = filesObservable.indexOf(dropTarget);
        if (fromIndex < 0 || toIndex < 0) return;

        filesObservable.remove(draggedItem);
        filesObservable.add(toIndex, draggedItem);

        // Recalcular campo "orden"
        for (int i = 0; i < filesObservable.size(); i++) {
            var rel = filesObservable.get(i);
            rel.setOrden(i + 1);
        }
    }

    /**
     * Métodos de compatibilidad para código existente que aún usa la nomenclatura de imágenes
     */
    @Deprecated
    public void addImagen(ToBucketFileRelation nuevaImagen) {
        addFile(nuevaImagen);
    }

    @Deprecated
    public void removeImagen(ToBucketFileRelation imagen) {
        removeFile(imagen);
    }

    @Deprecated
    public void reorderImagen(ToBucketFileRelation draggedItem, ToBucketFileRelation dropTarget) {
        reorderFile(draggedItem, dropTarget);
    }

    @Deprecated
    public ObservableList<ToBucketFileRelation> getImagenesObservable() {
        return filesObservable;
    }
}
