package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import corp.jamaro.jamaroescritoriofx.appfx.service.GeneralService;
import corp.jamaro.jamaroescritoriofx.appfx.service.NavigationService;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.util.LoadingUtil;
import corp.jamaro.jamaroescritoriofx.connection.security.SecurityContext;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.UniversalSaleGuiDto;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.MainSaleGuiService;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.UniversalSaleGuiService;
import javafx.fxml.FXML;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.layout.StackPane;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class UnisersalSaleGuiController extends BaseController {

    // Inyección de dependencias
    private final UniversalSaleGuiService universalSaleGuiService;
    private final MainSaleGuiService mainSaleGuiService;
    private final GeneralService generalService;
    private final SecurityContext securityContext;
    private final SpringFXMLLoader springFXMLLoader;
    private final AlertUtil alertUtil;
    private final ConnectionService connectionService;
    private final NavigationService navigationService;

    @FXML
    private Label lblInfo;

    @FXML
    private ProgressIndicator loadingIndicator;

    @FXML
    private Menu mAuditar;

    @FXML
    private Menu mOpciones;

    @FXML
    private MenuBar mbUniversal;

    @FXML
    private MenuItem miLogOut;

    @FXML
    private StackPane spUniversal;

    // Tab principal (MainSaleGui del usuario actual) – no debe ser cerrable.
    @FXML
    private Tab tabPrincipal;

    // TabPane que contendrá el tab principal y los tabs de auditoría.
    @FXML
    private TabPane tpUniversal;

    // Mapa para relacionar cada MainSaleGuiId de auditoría con su Tab.
    private final Map<UUID, Tab> auditingTabs = new ConcurrentHashMap<>();

    // Referencia actual del UniversalSaleGuiDto que se actualiza con los datos del servidor.
    private UniversalSaleGuiDto currentUGuiDto;

    @Override
    public void initialize(URL url, java.util.ResourceBundle resourceBundle) {
        // Permitir cierre de tabs en el TabPane (los tabs que se marquen como closable mostrarán su "X")
        tpUniversal.setTabClosingPolicy(TabPane.TabClosingPolicy.ALL_TABS);
        // Asegurarse de que el tab principal no tenga botón de cierre.
        tabPrincipal.setClosable(false);

        // Verificar autenticación
        if (!securityContext.isAuthenticated() || securityContext.getAuthenticatedUser() == null) {
            log.error("No hay usuario autenticado en SecurityContext. Se aborta la inicialización.");
            return;
        }

        updateLblInfo();

        // Suscribirse al flujo de cambios de UniversalSaleGuiDto
        UUID userId = securityContext.getAuthenticatedUser().getId();
        log.info("Suscribiéndose a UniversalSaleGuiDto del usuario con ID: {}", userId);
        Disposable subscription = LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                universalSaleGuiService.subscribeToChanges(userId),
                this::handleUniversalSaleGuiUpdate,
                this::handleError,
                () -> log.info("Finalizó la suscripción (onComplete).")
        );
        registerSubscription(subscription);

        loadAndPopulateUsersMenu();

        miLogOut.setOnAction(e -> handleLogOut());
    }

    /**
     * Actualiza el lblInfo con el nombre del usuario y la primera letra de sus apellidos.
     * Ejemplo: "Juan G."
     */
    private void updateLblInfo() {
        User authUser = securityContext.getAuthenticatedUser();
        if (authUser == null) return;
        String firstLetter = "";
        if (authUser.getApellidos() != null && !authUser.getApellidos().isBlank()) {
            firstLetter = authUser.getApellidos().trim().substring(0, 1);
        }
        lblInfo.setText(authUser.getNombre() + " " + firstLetter + ".");
    }

    /**
     * Solicita la lista de todos los usuarios y llena el menú mAuditar con MenuItems,
     * omitiendo al usuario autenticado. Si se recibe un error con "Access Denied", se oculta mAuditar.
     */
    private void loadAndPopulateUsersMenu() {
        generalService.getAllUsers()
                .collectList()
                .subscribe(
                        users -> runOnUiThread(() -> populateAuditarMenu(users)),
                        error -> {
                            log.error("Error al obtener la lista de usuarios", error);
                            runOnUiThread(() -> {
                                if (error.getMessage() != null && error.getMessage().contains("Access Denied")) {
                                    mAuditar.setVisible(false);
                                } else {
                                    alertUtil.showError("Error al obtener la lista de usuarios: " + error.getMessage());
                                }
                            });
                        }
                );
    }

    /**
     * Construye los MenuItem dentro de mAuditar a partir de la lista de usuarios,
     * excluyendo al usuario autenticado.
     */
    private void populateAuditarMenu(java.util.List<User> users) {
        UUID currentUserId = securityContext.getAuthenticatedUser().getId();
        mAuditar.getItems().clear();
        for (User user : users) {
            if (user.getId().equals(currentUserId)) continue;
            String label = user.getNombre() + " " +
                    (user.getDocumento() != null ? user.getDocumento() : "(doc)");
            MenuItem item = new MenuItem(label);
            item.setOnAction(e -> handleAddAuditingForUser(user));
            mAuditar.getItems().add(item);
        }
    }

    /**
     * Maneja la acción de auditar el MainSaleGui de otro usuario:
     * 1) Obtiene el MainSaleGui del usuario (por su username).
     * 2) Llama a universalSaleGuiService.addAuditedMainSale(...) para agregar su ID a la auditoría.
     */
    private void handleAddAuditingForUser(User otherUser) {
        if (currentUGuiDto == null) {
            alertUtil.showError("No hay UniversalSaleGui activo. Intente nuevamente.");
            return;
        }
        mainSaleGuiService.getMainSaleGuiOfUser(otherUser.getUsername())
                .flatMap(mainSaleGui ->
                        universalSaleGuiService.addAuditedMainSale(currentUGuiDto.getId(), mainSaleGui.getId()))
                .subscribe(
                        updatedDto -> log.info("Se agregó auditoría para el MainSaleGui de '{}'.", otherUser.getUsername()),
                        error -> {
                            log.error("Error agregando auditoría para '{}'.", otherUser.getUsername(), error);
                            runOnUiThread(() ->
                                    alertUtil.showError("No se pudo auditar al usuario " + otherUser.getUsername() +
                                            ": " + error.getMessage()));
                        }
                );
    }

    /**
     * Se invoca cada vez que el servidor emite un nuevo UniversalSaleGuiDto.
     */
    private void handleUniversalSaleGuiUpdate(UniversalSaleGuiDto updated) {
        runOnUiThread(() -> {
            this.currentUGuiDto = updated;
            log.debug("Recibido UniversalSaleGuiDto: {}", updated);
            buildOrUpdatePrincipalTab();
            updateAuditingTabs();
        });
    }

    /**
     * Construye o actualiza el tab principal (MainSaleGui del usuario autenticado),
     * basándose en currentUGuiDto.getMainSaleGuiId().
     */
    private void buildOrUpdatePrincipalTab() {
        UUID mainSaleGuiId = currentUGuiDto.getMainSaleGuiId();
        if (mainSaleGuiId == null) {
            log.warn("El UniversalSaleGuiDto no contiene mainSaleGuiId.");
            return;
        }
        if (tabPrincipal.getContent() == null) {
            Parent mainSaleView = springFXMLLoader.load(FXMLEnum.MAIN_SALE);
            MainSaleGuiController controller = springFXMLLoader.getController(mainSaleView);
            controller.setMainSaleGuiId(mainSaleGuiId);
            tabPrincipal.setContent(mainSaleView);
        } else {
            log.debug("El MainSaleGui ya está asignado en el tab principal. Refrescar si se requiere nueva información.");
        }
    }

    /**
     * Actualiza los tabs de auditoría en función de currentUGuiDto.getAuditingMainSales():
     * - Elimina aquellos que ya no estén.
     * - Agrega nuevos tabs para los IDs que aparezcan.
     */
    private void updateAuditingTabs() {
        Set<UUID> auditingIdsFromServer = currentUGuiDto.getAuditingMainSales();
        if (auditingIdsFromServer == null) {
            auditingIdsFromServer = Collections.emptySet();
        }
        // Remover tabs que ya no existen
        for (Iterator<Map.Entry<UUID, Tab>> it = auditingTabs.entrySet().iterator(); it.hasNext(); ) {
            Map.Entry<UUID, Tab> entry = it.next();
            UUID mainSaleId = entry.getKey();
            if (!auditingIdsFromServer.contains(mainSaleId)) {
                tpUniversal.getTabs().remove(entry.getValue());
                it.remove();
                log.info("Removido tab de auditoría para MainSaleGuiId={}", mainSaleId);
            }
        }
        // Agregar tabs nuevos
        for (UUID mainSaleGuiId : auditingIdsFromServer) {
            if (!auditingTabs.containsKey(mainSaleGuiId)) {
                Tab newAuditingTab = createAuditingTab(mainSaleGuiId);
                auditingTabs.put(mainSaleGuiId, newAuditingTab);
                tpUniversal.getTabs().add(newAuditingTab);
                log.info("Agregado tab de auditoría para MainSaleGuiId={}", mainSaleGuiId);
            }
        }
    }

    /**
     * Crea un tab para auditar el MainSaleGui de otro usuario (por su ID).
     * El título del tab se actualizará con el usernameOwner obtenido del flujo.
     * El tab es cerrable y, al cerrarse, se quita del auditing en el servidor.
     */
    private Tab createAuditingTab(UUID mainSaleGuiId) {
        Tab tab = new Tab();
        tab.setClosable(true);
        Parent mainSaleView = springFXMLLoader.load(FXMLEnum.MAIN_SALE);
        MainSaleGuiController controller = springFXMLLoader.getController(mainSaleView);
        controller.setMainSaleGuiId(mainSaleGuiId);
        tab.setContent(mainSaleView);
        // Suscribirse para actualizar el título con el usernameOwner
        Disposable sub = mainSaleGuiService.subscribeToChanges(mainSaleGuiId)
                .take(1)
                .subscribe(
                        mainSale -> runOnUiThread(() -> {
                            String ownerName = mainSale.getUsernameOwner();
                            tab.setText("Auditar - " + ownerName);
                        }),
                        error -> log.error("No se pudo obtener usernameOwner de MainSaleGuiId={}", mainSaleGuiId, error)
                );
        registerSubscription(sub);
        // Al cerrar el tab, se remueve la auditoría en el servidor
        tab.setOnCloseRequest(event -> {
            if (currentUGuiDto == null) {
                log.warn("No hay currentUGuiDto. No se puede remover auditoría.");
                return;
            }
            UniversalSaleGuiService.RemoveAuditedMainSaleRequest request =
                    new UniversalSaleGuiService.RemoveAuditedMainSaleRequest(currentUGuiDto.getId(), mainSaleGuiId);
            universalSaleGuiService.removeAuditedMainSale(request.universalSaleGuiId(), request.mainSaleGuiId())
                    .subscribe(
                            updatedDto -> log.info("Se removió la auditoría para MainSaleGuiId={}", mainSaleGuiId),
                            error -> {
                                log.error("Error al remover auditoría", error);
                                runOnUiThread(() -> {
                                    alertUtil.showError("No se pudo remover auditoría: " + error.getMessage());
                                    event.consume();
                                });
                            }
                    );
        });
        return tab;
    }

    private void handleError(Throwable error) {
        log.error("Error en la suscripción de UniversalSaleGuiDto: {}", error.getMessage(), error);
        runOnUiThread(() ->
                alertUtil.showError("Error al recibir datos de UniversalSaleGui: " + error.getMessage()));
    }

    /**
     * Lógica para desloguear:
     * 1) Cerrar la vista actual (cancelar suscripciones con onClose()).
     * 2) Llamar a connectionService.logout() para limpiar el contexto.
     * 3) Navegar a la vista LOGIN_RFID usando navigationService.
     */
    private void handleLogOut() {
        log.info("Logout seleccionado. Cerrando la vista y navegando a LoginRFID.");
        onClose();
        connectionService.logout();
        navigationService.navigateTo(FXMLEnum.LOGIN_RFID)
                .subscribe(
                        unused -> log.info("Navegación a LOGIN_RFID exitosa."),
                        error -> runOnUiThread(() ->
                                alertUtil.showError("Error al navegar a pantalla de login: " + error.getMessage()))
                );
    }

    @Override
    public void onClose() {
        super.onClose();
        log.info("UnisersalSaleGuiController cerrado.");
    }
}
