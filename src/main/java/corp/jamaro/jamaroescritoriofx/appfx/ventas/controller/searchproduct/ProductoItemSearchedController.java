package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Atributo;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.CodigoFabrica;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Filtro;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Item;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Producto;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoNombre;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.FiltroDatoRellenado;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.SearchProductGui;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.SearchProductGuiService;
import javafx.application.Platform;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.fxml.FXML;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.beans.property.SimpleStringProperty;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Priority;
import javafx.scene.layout.VBox;
import org.kordamp.ikonli.fontawesome5.FontAwesomeSolid;
import org.kordamp.ikonli.javafx.FontIcon;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class ProductoItemSearchedController extends BaseController {

    private final SearchProductGuiService searchProductGuiService;

    private Producto producto;
    private SearchProductGui searchProductGui;
    private NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();

    // Definición de anchos para columnas fijas
    // Marca
    private final double MARCA_MIN_WIDTH = 60;
    private final double MARCA_PREF_WIDTH = 90;
    private final double MARCA_MAX_WIDTH = 180;

    // Código Fábrica
    private final double CODIGO_FABRICA_MIN_WIDTH = 90;
    private final double CODIGO_FABRICA_PREF_WIDTH = 120;
    private final double CODIGO_FABRICA_MAX_WIDTH = 180;

    // Ubicación
    private final double UBICACION_MIN_WIDTH = 90;
    private final double UBICACION_PREF_WIDTH = 120;
    private final double UBICACION_MAX_WIDTH = 180;

    // Precio Venta Base (P.V 1)
    private final double PRECIO_VENTA_BASE_MIN_WIDTH = 60;
    private final double PRECIO_VENTA_BASE_PREF_WIDTH = 60;
    private final double PRECIO_VENTA_BASE_MAX_WIDTH = 60;

    // Precio Venta Público (P.V 2)
    private final double PRECIO_VENTA_PUBLICO_MIN_WIDTH = 60;
    private final double PRECIO_VENTA_PUBLICO_PREF_WIDTH = 60;
    private final double PRECIO_VENTA_PUBLICO_MAX_WIDTH = 60;

    // Stock Total (Q)
    private final double STOCK_TOTAL_MIN_WIDTH = 45;
    private final double STOCK_TOTAL_PREF_WIDTH = 45;
    private final double STOCK_TOTAL_MAX_WIDTH = 45;

    // Propiedad para controlar si el panel está expandido o contraído
    private final BooleanProperty expanded = new SimpleBooleanProperty(false);

    // Indicador visual de expansión/contracción
    private FontIcon expandIcon;

    @FXML
    private HBox hbFiltrosAtributos;

    @FXML
    private Label lblCodProductoOld;

    @FXML
    private Label lblDescripcion;

    @FXML
    private TableView<Item> tvItems;

    @FXML
    private TableColumn<Item, String> marcaColumn;

    @FXML
    private TableColumn<Item, String> codigoFabricaColumn;

    // Las columnas de atributos se crearán dinámicamente

    @FXML
    private TableColumn<Item, String> ubicacionColumn;

    @FXML
    private TableColumn<Item, String> precioVentaBaseColumn;

    @FXML
    private TableColumn<Item, String> precioVentaPublicoColumn;

    @FXML
    private TableColumn<Item, String> stockTotalColumn;

    @FXML
    private AnchorPane root;

    @FXML
    private VBox mainVBox;

    @FXML
    private VBox vbCodigosFabrica;

    @FXML
    private VBox vbVehiculos;

    private SearchProductGuiController.ItemClickHandler itemClickHandler;
    private SearchProductGuiController.ItemDoubleClickHandler itemDoubleClickHandler;

    /**
     * Establece el manejador para eventos de clic en items
     * @param handler Función que recibe el item y su producto padre
     */
    public void setItemClickHandler(SearchProductGuiController.ItemClickHandler handler) {
        this.itemClickHandler = handler;
    }

    /**
     * Establece el manejador para eventos de doble clic en items
     * @param handler Función que recibe el item y su producto padre
     */
    public void setItemDoubleClickHandler(SearchProductGuiController.ItemDoubleClickHandler handler) {
        this.itemDoubleClickHandler = handler;
    }

    @Override
    public void initialize(java.net.URL url, java.util.ResourceBundle resourceBundle) {
        // Configurar las columnas del TableView
        configureTableColumns();

        // Crear el icono de expansión
        expandIcon = new FontIcon(FontAwesomeSolid.CHEVRON_DOWN);
        expandIcon.setIconSize(18);
        expandIcon.getStyleClass().add("font-icon-light");

        // Configurar el evento de clic directamente en el root para mejorar la respuesta
        root.setOnMouseClicked(this::handleCellClick);

        // Agregar cursor de mano para indicar que es clickeable
        root.setCursor(javafx.scene.Cursor.HAND);

        // Inicialmente ocultar el TableView
        tvItems.setVisible(false);
        tvItems.setManaged(false);

        // Configurar el TableView para que respete los anchos preferidos de las columnas
        // y distribuya el espacio restante proporcionalmente
        // Reemplazando el uso de CONSTRAINED_RESIZE_POLICY (deprecated) con el método recomendado
        tvItems.setColumnResizePolicy(TableView.UNCONSTRAINED_RESIZE_POLICY);
        tvItems.setFixedCellSize(25); // Establecer altura fija para las celdas

        // Hacer que las columnas no sean reordenables
        tvItems.getColumns().forEach(column -> column.setReorderable(false));

        // Agregar listener para ajustar las columnas dinámicas cuando cambie el tamaño de la tabla
        tvItems.widthProperty().addListener((obs, oldVal, newVal) -> {
            if (expanded.get() && !tvItems.getItems().isEmpty()) {
                // Solo recalcular si hay un cambio significativo en el ancho (más de 5px)
                if (Math.abs(oldVal.doubleValue() - newVal.doubleValue()) > 5) {
                    log.debug("Ancho de tabla cambiado de {} a {}, recalculando anchos de columnas", oldVal, newVal);
                    adjustDynamicColumnsWidth();
                }
            }
        });

        // Configurar el row factory para aplicar estilos especiales a filas con stock cero o negativo
        tvItems.setRowFactory(tv -> {
            TableRow<Item> row = new TableRow<Item>() {
                @Override
                protected void updateItem(Item item, boolean empty) {
                    super.updateItem(item, empty);

                    if (item == null || empty) {
                        setStyle("");
                        getStyleClass().removeAll("zero-stock-row");
                        return;
                    }

                    // Verificar si el stock es cero o negativo
                    if (item.getStockTotal() != null && item.getStockTotal() <= 0) {
                        // Aplicar clase CSS para filas con stock cero o negativo
                        if (!getStyleClass().contains("zero-stock-row")) {
                            getStyleClass().add("zero-stock-row");
                        }
                    } else {
                        // Remover la clase si el stock es positivo
                        getStyleClass().removeAll("zero-stock-row");
                    }
                }
            };
            return row;
        });

        // Aplicar estilos CSS
        root.getStyleClass().add("producto-item");
        tvItems.getStyleClass().add("items-table-view");

        // Aplicar la clase CSS para vbVehiculos según las preferencias del usuario
        vbVehiculos.getStyleClass().add("vb-vehiculos");

        // Configurar eventos de clic y doble clic en la tabla de items
        tvItems.setOnMouseClicked(event -> {
            Item selectedItem = tvItems.getSelectionModel().getSelectedItem();
            if (selectedItem != null && producto != null) {
                if (event.getClickCount() == 2 && itemDoubleClickHandler != null) {
                    // Doble clic en item
                    log.debug("Doble clic en item: {} del producto: {}",
                             selectedItem.getId(), producto.getCodProductoOld());
                    itemDoubleClickHandler.handle(selectedItem, producto);
                    event.consume();
                } else if (event.getClickCount() == 1 && itemClickHandler != null) {
                    // Clic simple en item
                    log.debug("Clic simple en item: {} del producto: {}",
                             selectedItem.getId(), producto.getCodProductoOld());
                    itemClickHandler.handle(selectedItem, producto);
                    event.consume();
                }
            }
        });
    }

    /**
     * Configura las columnas del TableView
     */
    private void configureTableColumns() {
        // Columna Marca (Camel Case)
        marcaColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            String marcaText = item.getMarca() != null && item.getMarca().getNombre() != null ?
                    toCamelCase(item.getMarca().getNombre()) : "Sin marca";
            return new SimpleStringProperty(marcaText);
        });
        marcaColumn.setReorderable(false);
        marcaColumn.setMinWidth(MARCA_MIN_WIDTH);
        marcaColumn.setPrefWidth(MARCA_PREF_WIDTH);
        marcaColumn.setMaxWidth(MARCA_MAX_WIDTH);

        // Columna Código Fábrica (UPPERCASE concatenation)
        codigoFabricaColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            if (item.getCodigosFabrica() == null || item.getCodigosFabrica().isEmpty()) {
                return new SimpleStringProperty("");
            }
            String codigosFabrica = item.getCodigosFabrica().stream()
                    .map(cf -> cf.getCodigo() != null ? cf.getCodigo().toUpperCase() : "")
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.joining(", "));
            return new SimpleStringProperty(codigosFabrica);
        });
        codigoFabricaColumn.setReorderable(false);
        codigoFabricaColumn.setMinWidth(CODIGO_FABRICA_MIN_WIDTH);
        codigoFabricaColumn.setPrefWidth(CODIGO_FABRICA_PREF_WIDTH);
        codigoFabricaColumn.setMaxWidth(CODIGO_FABRICA_MAX_WIDTH);

        // Las columnas de atributos se crearán dinámicamente cuando se carguen los items

        // Columna Ubicación
        ubicacionColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            if (item.getUbicaciones() == null || item.getUbicaciones().isEmpty()) {
                return new SimpleStringProperty("");
            }
            String ubicacionesText = item.getUbicaciones().stream()
                    .map(u -> u.getNombre() != null ? u.getNombre() : "")
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.joining(", "));
            return new SimpleStringProperty(ubicacionesText);
        });
        ubicacionColumn.setReorderable(false);
        ubicacionColumn.setMinWidth(UBICACION_MIN_WIDTH);
        ubicacionColumn.setPrefWidth(UBICACION_PREF_WIDTH);
        ubicacionColumn.setMaxWidth(UBICACION_MAX_WIDTH);

        // Columna Precio Venta Base (P.V 1)
        precioVentaBaseColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            if (item.getPrecioVentaBase() == null) {
                return new SimpleStringProperty("");
            }
            return new SimpleStringProperty(currencyFormat.format(item.getPrecioVentaBase()));
        });
        precioVentaBaseColumn.setReorderable(false);
        precioVentaBaseColumn.setMinWidth(PRECIO_VENTA_BASE_MIN_WIDTH);
        precioVentaBaseColumn.setPrefWidth(PRECIO_VENTA_BASE_PREF_WIDTH);
        precioVentaBaseColumn.setMaxWidth(PRECIO_VENTA_BASE_MAX_WIDTH);

        // Columna Precio Venta Público (P.V 2)
        precioVentaPublicoColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            if (item.getPrecioVentaPublico() == null) {
                return new SimpleStringProperty("");
            }
            return new SimpleStringProperty(currencyFormat.format(item.getPrecioVentaPublico()));
        });
        precioVentaPublicoColumn.setReorderable(false);
        precioVentaPublicoColumn.setMinWidth(PRECIO_VENTA_PUBLICO_MIN_WIDTH);
        precioVentaPublicoColumn.setPrefWidth(PRECIO_VENTA_PUBLICO_PREF_WIDTH);
        precioVentaPublicoColumn.setMaxWidth(PRECIO_VENTA_PUBLICO_MAX_WIDTH);

        // Columna Stock Total (Q)
        stockTotalColumn.setCellValueFactory(cellData -> {
            Item item = cellData.getValue();
            if (item.getStockTotal() == null) {
                return new SimpleStringProperty("");
            }
            return new SimpleStringProperty(item.getStockTotal().toString());
        });
        stockTotalColumn.setReorderable(false);
        stockTotalColumn.setMinWidth(STOCK_TOTAL_MIN_WIDTH);
        stockTotalColumn.setPrefWidth(STOCK_TOTAL_PREF_WIDTH);
        stockTotalColumn.setMaxWidth(STOCK_TOTAL_MAX_WIDTH);
    }

    /**
     * Convierte un texto a Camel Case (primera letra de cada palabra en mayúscula)
     */
    private String toCamelCase(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = true;

        for (char c : text.toLowerCase().toCharArray()) {
            if (Character.isSpaceChar(c) || c == '-' || c == '_') {
                nextUpperCase = true;
            } else if (nextUpperCase) {
                c = Character.toUpperCase(c);
                nextUpperCase = false;
            }

            if (!Character.isSpaceChar(c) && c != '-' && c != '_') {
                result.append(c);
            } else if (!result.isEmpty()) {
                result.append(' ');
            }
        }

        return result.toString();
    }

    /**
     * Configura el controlador con un producto
     */
    public void setProducto(Producto producto) {
        this.producto = producto;
        updateProductoUI();
        // No cargamos los items aquí, solo cuando se expande

        // Resetear el estado de expansión
        expanded.set(false);

        // Si hay un controlador expandido actualmente, asegurarse de que se contraiga
        if (tvItems.isVisible()) {
            tvItems.setVisible(false);
            tvItems.setManaged(false);
            tvItems.getItems().clear();
        }
    }

    /**
     * Configura el controlador con el SearchProductGui
     */
    public void setSearchProductGui(SearchProductGui searchProductGui) {
        this.searchProductGui = searchProductGui;
        // Si ya tenemos un producto, actualizar la UI para reflejar el nuevo orden de atributos
        if (this.producto != null) {
            updateProductoUI();
        }
    }

    /**
     * Método público para manejar los clics desde la celda del ListView principal
     */
    public void handleCellClick(MouseEvent event) {
        // Consumir el evento para evitar que se propague
        event.consume();

        // Capturar el producto actual como efectivamente final para el log
        final Producto currentProducto = this.producto;

        // Llamar al método privado para expandir/contraer en el hilo de UI
        runOnUiThread(() -> toggleExpanded());

        // Registrar el evento para depuración
        log.debug("Clic detectado en producto {}, cambiando estado de expansión a {}",
                 currentProducto != null ? currentProducto.getCodProductoOld() : "null",
                 !expanded.get());
    }

    /**
     * Devuelve el estado actual de expansión del producto
     */
    public boolean isExpanded() {
        return expanded.get();
    }

    /**
     * Devuelve el ID o código del producto para identificación
     */
    public String getProductoId() {
        return producto != null ? producto.getCodProductoOld() : "desconocido";
    }

    /**
     * Establece el estado de expansión del producto directamente sin hacer toggle
     * @param expand true para expandir, false para contraer
     */
    public void setExpanded(boolean expand) {
        // Capturar el ID del producto para usar en lambdas
        final String productoId = getProductoId();

        // Registrar el intento de cambio de estado
        log.debug("Intento de cambiar estado de expansión para producto {} de {} a {} (programaticamente)",
                 productoId, expanded.get(), expand);

        // Incluso si el estado es el mismo, forzar la actualización de la UI
        // Esto ayuda en casos donde la UI no refleja correctamente el estado actual
        expanded.set(expand);

        // Actualizar la UI según el nuevo estado
        updateExpandedState();

        // Forzar una actualización adicional del layout después de un breve retraso
        // Esto ayuda a asegurar que los cambios se apliquen correctamente
        Platform.runLater(() -> {
            log.debug("Forzando actualización adicional del layout para producto {}", productoId);
            root.requestLayout();
            root.applyCss();
            root.layout();
        });
    }

    /**
     * Maneja la expansión/contracción del panel
     */
    private void toggleExpanded() {
        if (producto == null) {
            log.warn("No se puede expandir/contraer: producto es null");
            return;
        }

        // Capturar el producto actual como efectivamente final para el log
        final Producto currentProducto = this.producto;

        // Cambiar el estado de expansión
        boolean newValue = !expanded.get();
        expanded.set(newValue);

        log.debug("Cambiando estado de expansión para producto {} a {}", currentProducto.getCodProductoOld(), newValue);

        // Actualizar la UI según el nuevo estado
        updateExpandedState();
    }

    /**
     * Actualiza la UI según el estado de expansión actual
     */
    private void updateExpandedState() {
        final boolean isExpanded = expanded.get();
        final String productoId = getProductoId(); // Capture for lambda

        log.debug("Actualizando UI para producto {} a estado expandido={}", productoId, isExpanded);

        // Aplicar inmediatamente los cambios de estilo
        if (isExpanded) {
            // Expandir
            if (!root.getStyleClass().contains("producto-item-expanded")) {
                root.getStyleClass().add("producto-item-expanded");
            }
            expandIcon.setIconCode(FontAwesomeSolid.CHEVRON_UP);

            // Hacer visible el TableView inmediatamente para dar feedback visual
            tvItems.setVisible(true);
            tvItems.setManaged(true);

            // Establecer una altura mínima mientras se cargan los items
            if (tvItems.getItems().isEmpty()) {
                tvItems.setPrefHeight(50);
                tvItems.setMinHeight(50);
            }

            // Solo cargar los items cuando se expande por primera vez
            if (tvItems.getItems().isEmpty()) {
                // Cargar los items (esto actualizará la altura cuando termine)
                loadItems();
            } else {
                // Si ya hay items, ajustar la altura inmediatamente
                adjustTableViewHeight();
            }
        } else {
            // Contraer
            root.getStyleClass().remove("producto-item-expanded");
            expandIcon.setIconCode(FontAwesomeSolid.CHEVRON_DOWN);

            // Ocultar el TableView inmediatamente
            tvItems.setVisible(false);
            tvItems.setManaged(false);
            tvItems.setPrefHeight(0);
            tvItems.setMinHeight(0);
            tvItems.setMaxHeight(0);
        }

        // Forzar la actualización del layout inmediatamente usando runOnUiThread del BaseController
        runOnUiThread(() -> {
            root.requestLayout();
            root.applyCss();
            root.layout();
            log.debug("Layout actualizado para producto {}", productoId);

            // Forzar una actualización adicional después de un breve retraso
            // Esto es especialmente importante cuando hay scroll involucrado
            Platform.runLater(() -> {
                if (isExpanded) {
                    // Si estamos expandiendo, asegurar que el TableView sea visible y tenga la altura correcta
                    tvItems.setVisible(true);
                    tvItems.setManaged(true);
                    if (!tvItems.getItems().isEmpty()) {
                        adjustTableViewHeight();
                    }
                    log.debug("Actualización adicional para producto expandido {}", productoId);
                }
            });
        });
    }

    /**
     * Ajusta la altura del TableView para mostrar todos los items con un cálculo más preciso
     */
    private void adjustTableViewHeight() {
        if (!expanded.get() || tvItems.getItems().isEmpty()) return;

        // Calcular la altura necesaria para mostrar todos los items
        // Usar el valor de fixedCellSize establecido anteriormente
        double itemHeight = tvItems.getFixedCellSize();
        int itemCount = tvItems.getItems().size();

        // Ajustar para la estructura de columnas (ahora solo un nivel)
        // Reducir el header height para optimizar espacio
        double headerHeight = 30;
        double totalHeight = itemCount * itemHeight + headerHeight;

        // Establecer la altura exacta del TableView para mostrar todos los items sin scroll
        tvItems.setPrefHeight(totalHeight);
        tvItems.setMaxHeight(totalHeight);
        tvItems.setMinHeight(totalHeight);

        // Desactivar el scroll
        tvItems.setMouseTransparent(false); // Asegurar que los eventos de mouse lleguen al TableView
        tvItems.setFocusTraversable(false); // Evitar que el TableView tome el foco

        // Solicitar un nuevo layout inmediatamente
        root.requestLayout();

        // Forzar una actualización del layout para asegurar que se aplique la altura
        Platform.runLater(() -> {
            tvItems.layout();
            root.layout();
        });

        log.debug("Altura calculada para TableView: {} para {} items", totalHeight, itemCount);
    }

    /**
     * Actualiza la UI con la información del producto
     */
    private void updateProductoUI() {
        if (producto == null) return;

        // Código y descripción
        lblCodProductoOld.setText(producto.getCodProductoOld() != null ? producto.getCodProductoOld() : "");
        // Usar clases CSS en lugar de estilos inline
        lblCodProductoOld.getStyleClass().add("producto-codigo");

        // Apply camel case to product description as per user preference
        lblDescripcion.setText(producto.getDescripcion() != null ? toCamelCase(producto.getDescripcion()) : "");
        // Usar clases CSS en lugar de estilos inline
        lblDescripcion.getStyleClass().add("producto-descripcion");

        // Códigos de fábrica
        vbCodigosFabrica.getChildren().clear();
        if (producto.getCodigosFabrica() != null && !producto.getCodigosFabrica().isEmpty()) {
            producto.getCodigosFabrica().stream()
                .map(CodigoFabrica::getCodigo)
                .filter(Objects::nonNull)
                .forEach(codigo -> {
                    Label label = new Label(codigo);
                    label.setAlignment(Pos.CENTER);
                    label.setMaxWidth(Double.MAX_VALUE);
                    label.setWrapText(true);
                    label.getStyleClass().add("codigo-fabrica-label");
                    vbCodigosFabrica.getChildren().add(label);
                });
        } else {
            // Si no hay códigos de fábrica, agregar un espacio en blanco para mantener la estructura
            Label emptyLabel = new Label("");
            emptyLabel.setMinHeight(1); // Altura mínima reducida
            vbCodigosFabrica.getChildren().add(emptyLabel);
        }

        // Atributos
        hbFiltrosAtributos.getChildren().clear();
        if (producto.getAtributos() != null && !producto.getAtributos().isEmpty()) {
            // Ordenar atributos según la nueva lógica
            List<Atributo> sortedAtributos = sortAtributos(producto.getAtributos());

            // Mostrar los atributos ordenados
            sortedAtributos.forEach(atributo -> {
                HBox atributoBox = createAtributoBox(atributo);
                hbFiltrosAtributos.getChildren().add(atributoBox);
            });
        }

        // Vehículos
        vbVehiculos.getChildren().clear();
        if (producto.getVehiculos() != null && !producto.getVehiculos().isEmpty()) {
            String vehiculosText = producto.getVehiculos().stream()
                .flatMap(v -> v.getNombres().stream())
                .map(VehiculoNombre::getNombre)
                .filter(Objects::nonNull)
                .map(String::toUpperCase) // Convertir a mayúsculas según preferencia del usuario
                .collect(Collectors.joining(", "));

            Label label = new Label(vehiculosText);
            label.setAlignment(Pos.CENTER_LEFT); // Alineación a la izquierda
            label.setMaxWidth(Double.MAX_VALUE);
            label.setWrapText(true);
            // Ya no necesitamos establecer el tamaño de fuente aquí, se hace a través de CSS
            label.getStyleClass().add("vehiculos-label"); // Añadir clase CSS para estilos adicionales
            vbVehiculos.getChildren().add(label);
        } else {
            // Si no hay vehículos, agregar un espacio en blanco para mantener la estructura
            Label emptyLabel = new Label("");
            emptyLabel.setMinHeight(1); // Altura mínima reducida
            vbVehiculos.getChildren().add(emptyLabel);
        }

        // Forzar una actualización del layout para que se recalcule la altura
        Platform.runLater(() -> {
            root.requestLayout();
            root.applyCss();
            root.layout();
        });

        // Agregar el icono de expansión
        HBox expandIconContainer = new HBox(expandIcon);
        expandIconContainer.setAlignment(Pos.CENTER_RIGHT); // Mantener el icono a la derecha
        expandIconContainer.setPadding(new Insets(2, 10, 2, 0)); // Reducir el padding vertical
        expandIconContainer.setMaxWidth(Double.MAX_VALUE); // Permitir que ocupe todo el ancho
        HBox.setHgrow(expandIconContainer, Priority.ALWAYS); // Hacer que crezca para empujar el icono a la derecha
        vbVehiculos.getChildren().add(expandIconContainer);
    }

    /**
     * Crea un HBox para mostrar un atributo con su nombre y valor
     */
    private HBox createAtributoBox(Atributo atributo) {
        Label nombreLabel = new Label(atributo.getFiltro().getNombreFiltro() + ":");
        nombreLabel.getStyleClass().add("atributo-nombre");

        Label valorLabel = new Label(atributo.getDatoString());
        valorLabel.getStyleClass().add("atributo-valor");

        HBox box = new HBox(5, nombreLabel, valorLabel);
        box.setAlignment(Pos.CENTER);
        box.getStyleClass().add("atributo-box");
        box.setPadding(new Insets(2, 4, 2, 4)); // Reducir el padding para hacer el componente más compacto
        return box;
    }

    /**
     * Carga los items del producto
     */
    private void loadItems() {
        if (producto == null || producto.getId() == null) {
            log.warn("No se pueden cargar items: producto o ID es null");
            return;
        }

        log.debug("Iniciando carga de items para producto {}", getProductoId());

        // Mostrar un indicador de carga o mensaje mientras se cargan los items
        // Esto ayuda a dar feedback visual inmediato al usuario
        Label loadingLabel = new Label("Cargando items...");
        loadingLabel.getStyleClass().add("loading-label");
        tvItems.setPlaceholder(loadingLabel);

        // Asegurar que el TableView sea visible mientras se cargan los items
        tvItems.setVisible(true);
        tvItems.setManaged(true);

        // Capture producto.getId() as effectively final for lambda
        final UUID productoId = producto.getId();

        subscribeOnBoundedElastic(
            searchProductGuiService.getItemsByProductoId(productoId).collectList(),
            items -> Platform.runLater(() -> {
                // Verificar si el producto sigue expandido (podría haber cambiado mientras se cargaban los items)
                if (!expanded.get()) {
                    log.debug("El producto {} ya no está expandido, ignorando resultados de carga", getProductoId());
                    return;
                }

                // Limpiar items existentes
                tvItems.getItems().clear();

                // Solo agregar items que no sean nulos
                if (items != null && !items.isEmpty()) {
                    log.debug("Procesando {} items cargados para producto {}", items.size(), getProductoId());

                    // Crear columnas dinámicas para los atributos antes de agregar los items
                    createDynamicAtributoColumns(items);

                    // Ordenar los items por stock total de mayor a menor
                    List<Item> sortedItems = items.stream()
                        .sorted(Comparator.comparing(item -> {
                            // Usar 0.0 como valor predeterminado si stockTotal es null
                            return item.getStockTotal() != null ? item.getStockTotal() : 0.0;
                        }, Comparator.reverseOrder()))
                        .collect(Collectors.toList());

                    // Agregar los items ordenados al TableView
                    tvItems.getItems().addAll(sortedItems);
                    log.info("Cargados {} items para el producto {}, ordenados por stock de mayor a menor",
                             sortedItems.size(), getProductoId());

                    // Ajustar la altura del TableView inmediatamente
                    adjustTableViewHeight();

                    // Asegurar que el TableView sea visible y tenga los estilos correctos
                    tvItems.setVisible(true);
                    tvItems.setManaged(true);
                    if (!root.getStyleClass().contains("producto-item-expanded")) {
                        root.getStyleClass().add("producto-item-expanded");
                    }
                } else {
                    // Si no hay items, mostrar un mensaje pero mantener el panel expandido
                    log.info("No hay items para el producto {}", getProductoId());
                    Label noItemsLabel = new Label("No hay items disponibles");
                    noItemsLabel.getStyleClass().add("no-items-label");
                    tvItems.setPlaceholder(noItemsLabel);

                    // Ajustar la altura para mostrar el mensaje
                    tvItems.setPrefHeight(50);
                    tvItems.setMaxHeight(50);
                    tvItems.setMinHeight(50);
                }

                // Forzar una actualización del layout
                runOnUiThread(() -> {
                    root.requestLayout();
                    root.applyCss();
                    root.layout();
                    log.debug("Layout actualizado después de cargar items para producto {}", getProductoId());
                });
            }),
            error -> {
                log.error("Error al cargar items para producto {}: {}", getProductoId(), error.getMessage(), error);

                // Manejar el error en la UI
                Platform.runLater(() -> {
                    if (expanded.get()) { // Solo si sigue expandido
                        Label errorLabel = new Label("Error al cargar items");
                        errorLabel.getStyleClass().add("error-label");
                        tvItems.setPlaceholder(errorLabel);
                        tvItems.getItems().clear();
                    }
                });
            }
        );
    }

    /**
     * Crea columnas dinámicas para los atributos de los items
     * @param items Lista de items del producto
     */
    private void createDynamicAtributoColumns(List<Item> items) {
        // Eliminar columnas de atributos existentes (si las hay)
        List<TableColumn<Item, ?>> columnsToRemove = new ArrayList<>();
        for (TableColumn<Item, ?> column : tvItems.getColumns()) {
            if (column.getUserData() != null && "atributo".equals(column.getUserData().toString())) {
                columnsToRemove.add(column);
            }
        }
        tvItems.getColumns().removeAll(columnsToRemove);

        // Recopilar todos los filtros únicos de todos los items
        Set<Filtro> allFiltros = new HashSet<>();
        for (Item item : items) {
            if (item.getAtributos() != null) {
                for (Atributo atributo : item.getAtributos()) {
                    if (atributo.getFiltro() != null) {
                        allFiltros.add(atributo.getFiltro());
                    }
                }
            }
        }

        // Ordenar los filtros según el criterio (primero los que coinciden con FiltroDatoRellenado, luego el resto alfabéticamente)
        List<Filtro> sortedFiltros = sortFiltros(allFiltros);

        // Si no hay filtros, no crear columnas dinámicas
        if (sortedFiltros.isEmpty()) {
            return;
        }

        // Crear columnas para cada filtro
        int insertIndex = tvItems.getColumns().indexOf(codigoFabricaColumn) + 1;

        for (Filtro filtro : sortedFiltros) {
            TableColumn<Item, String> column = new TableColumn<>(filtro.getNombreFiltro());
            column.setUserData("atributo"); // Marcar como columna de atributo
            column.setReorderable(false);

            // Inicialmente establecer un ancho mínimo razonable
            double minColumnWidth = 80.0;
            column.setMinWidth(minColumnWidth);

            // Configurar el cell value factory para mostrar el valor del atributo correspondiente
            column.setCellValueFactory(cellData -> {
                Item item = cellData.getValue();
                if (item.getAtributos() == null) {
                    return new SimpleStringProperty("");
                }

                // Buscar el atributo que corresponde a este filtro
                for (Atributo atributo : item.getAtributos()) {
                    if (atributo.getFiltro() != null && atributo.getFiltro().getId().equals(filtro.getId())) {
                        return new SimpleStringProperty(atributo.getDatoString() != null ? atributo.getDatoString() : "");
                    }
                }

                // Si no se encuentra el atributo, devolver cadena vacía
                return new SimpleStringProperty("");
            });

            // Insertar la columna en la posición correcta
            tvItems.getColumns().add(insertIndex++, column);
        }

        // Asegurar que todas las columnas no sean reordenables
        tvItems.getColumns().forEach(column -> column.setReorderable(false));

        // Ajustar los anchos de las columnas dinámicas
        adjustDynamicColumnsWidth();
    }

    /**
     * Ajusta los anchos de las columnas dinámicas para distribuir el espacio disponible equitativamente
     */
    private void adjustDynamicColumnsWidth() {
        // Contar cuántas columnas dinámicas hay
        List<TableColumn<Item, ?>> dynamicColumns = new ArrayList<>();
        for (TableColumn<Item, ?> column : tvItems.getColumns()) {
            if (column.getUserData() != null && "atributo".equals(column.getUserData().toString())) {
                dynamicColumns.add(column);
            }
        }

        if (dynamicColumns.isEmpty()) {
            return;
        }

        // Calcular el ancho disponible para las columnas dinámicas
        // Sumamos los anchos preferidos de las columnas fijas
        double fixedColumnsWidth = MARCA_PREF_WIDTH + CODIGO_FABRICA_PREF_WIDTH + UBICACION_PREF_WIDTH +
                                  PRECIO_VENTA_BASE_PREF_WIDTH + PRECIO_VENTA_PUBLICO_PREF_WIDTH + STOCK_TOTAL_PREF_WIDTH;

        // Obtener el ancho actual de la tabla
        double currentTableWidth = tvItems.getWidth();

        // Si la tabla aún no tiene un ancho válido, usar una estimación
        if (currentTableWidth <= 0) {
            currentTableWidth = getEstimatedTableWidth();
        }

        // El ancho disponible para las columnas dinámicas es el ancho total menos el ancho de las columnas fijas
        double availableWidth = Math.max(currentTableWidth - fixedColumnsWidth, 300); // Al menos 300px

        // Calcular el ancho para cada columna dinámica (distribución equitativa)
        double columnWidth = availableWidth / dynamicColumns.size();

        // Establecer un ancho mínimo razonable para cada columna
        double minColumnWidth = 80.0;
        columnWidth = Math.max(columnWidth, minColumnWidth);

        log.debug("Ajustando columnas dinámicas - Ancho tabla: {}, Ancho columnas fijas: {}, Ancho disponible: {}, Columnas dinámicas: {}, Ancho por columna: {}",
                 currentTableWidth, fixedColumnsWidth, availableWidth, dynamicColumns.size(), columnWidth);

        // Aplicar el mismo ancho a todas las columnas dinámicas
        for (TableColumn<Item, ?> column : dynamicColumns) {
            column.setPrefWidth(columnWidth);
            log.debug("Ajustando columna {}: ancho={}", column.getText(), columnWidth);
        }
    }

    /**
     * Ordena los filtros según el criterio:
     * 1. Primero los que coinciden con FiltroDatoRellenado, ordenados por fila y columna
     * 2. Luego el resto ordenados alfabéticamente
     * Evita duplicados
     */
    private List<Filtro> sortFiltros(Set<Filtro> filtros) {
        // Si no hay SearchProductGui o no tiene FiltroDatoRellenado, usar orden alfabético
        if (searchProductGui == null || searchProductGui.getFiltroDatoRellenados() == null || searchProductGui.getFiltroDatoRellenados().isEmpty()) {
            return filtros.stream()
                .sorted((f1, f2) -> f1.getNombreFiltro().compareToIgnoreCase(f2.getNombreFiltro()))
                .collect(Collectors.toList());
        }

        // Ordenar FiltroDatoRellenado por fila y columna
        List<FiltroDatoRellenado> sortedFiltroDatos = searchProductGui.getFiltroDatoRellenados().stream()
            .filter(f -> f.getFiltro() != null)
            .sorted(Comparator.comparing(FiltroDatoRellenado::getFila)
                    .thenComparing(FiltroDatoRellenado::getColumna))
            .collect(Collectors.toList());

        // Conjunto para rastrear los filtros ya procesados (para evitar duplicados)
        Set<UUID> processedFiltroIds = new HashSet<>();

        // Lista para el resultado final ordenado
        List<Filtro> result = new ArrayList<>();

        // Mapa para agrupar FiltroDatoRellenado por fila
        Map<Integer, List<FiltroDatoRellenado>> filaToFiltroDatos = sortedFiltroDatos.stream()
            .collect(Collectors.groupingBy(FiltroDatoRellenado::getFila));

        // Procesar cada fila en orden
        filaToFiltroDatos.keySet().stream().sorted().forEach(fila -> {
            List<FiltroDatoRellenado> filaDatos = filaToFiltroDatos.get(fila);

            // Para cada FiltroDatoRellenado en esta fila
            for (FiltroDatoRellenado filtroDato : filaDatos) {
                // Si ya procesamos este filtro, omitirlo (evitar duplicados)
                if (processedFiltroIds.contains(filtroDato.getFiltro().getId())) {
                    continue;
                }

                // Agregar el filtro al resultado
                result.add(filtroDato.getFiltro());
                // Marcar este filtro como procesado
                processedFiltroIds.add(filtroDato.getFiltro().getId());
            }
        });

        // Agregar los filtros restantes ordenados alfabéticamente
        filtros.stream()
            .filter(f -> !processedFiltroIds.contains(f.getId()))
            .sorted((f1, f2) -> f1.getNombreFiltro().compareToIgnoreCase(f2.getNombreFiltro()))
            .forEach(result::add);

        return result;
    }

    /**
     * Ordena los atributos según la nueva lógica:
     * 1. Primero los que coinciden con FiltroDatoRellenado, ordenados por fila y columna
     * 2. Luego el resto ordenados alfabéticamente
     * Evita duplicados de Filtros que ya aparecen en filas anteriores
     */
    private List<Atributo> sortAtributos(Set<Atributo> atributos) {
        // Si no hay SearchProductGui o no tiene FiltroDatoRellenado, usar orden alfabético
        if (searchProductGui == null || searchProductGui.getFiltroDatoRellenados() == null || searchProductGui.getFiltroDatoRellenados().isEmpty()) {
            return atributos.stream()
                .filter(a -> a.getFiltro() != null && a.getDatoString() != null && !a.getDatoString().isEmpty())
                .sorted((a1, a2) -> {
                    String nombre1 = a1.getFiltro().getNombreFiltro();
                    String nombre2 = a2.getFiltro().getNombreFiltro();
                    return nombre1.compareToIgnoreCase(nombre2);
                })
                .collect(Collectors.toList());
        }

        // Filtrar atributos válidos
        List<Atributo> validAtributos = atributos.stream()
            .filter(a -> a.getFiltro() != null && a.getDatoString() != null && !a.getDatoString().isEmpty())
            .collect(Collectors.toList());

        // Ordenar FiltroDatoRellenado por fila y columna
        List<FiltroDatoRellenado> sortedFiltroDatos = searchProductGui.getFiltroDatoRellenados().stream()
            .filter(f -> f.getFiltro() != null)
            .sorted(Comparator.comparing(FiltroDatoRellenado::getFila)
                    .thenComparing(FiltroDatoRellenado::getColumna))
            .collect(Collectors.toList());

        // Conjunto para rastrear los filtros ya procesados (para evitar duplicados)
        Set<UUID> processedFiltroIds = new HashSet<>();

        // Lista para el resultado final ordenado
        List<Atributo> result = new ArrayList<>();

        // Mapa para agrupar FiltroDatoRellenado por fila
        Map<Integer, List<FiltroDatoRellenado>> filaToFiltroDatos = sortedFiltroDatos.stream()
            .collect(Collectors.groupingBy(FiltroDatoRellenado::getFila));

        // Procesar cada fila en orden
        filaToFiltroDatos.keySet().stream().sorted().forEach(fila -> {
            List<FiltroDatoRellenado> filaDatos = filaToFiltroDatos.get(fila);

            // Para cada FiltroDatoRellenado en esta fila
            for (FiltroDatoRellenado filtroDato : filaDatos) {
                // Si ya procesamos este filtro, omitirlo (evitar duplicados)
                if (processedFiltroIds.contains(filtroDato.getFiltro().getId())) {
                    continue;
                }

                // Buscar atributos que coincidan con este filtro
                for (Atributo atributo : validAtributos) {
                    if (atributo.getFiltro().getId().equals(filtroDato.getFiltro().getId())) {
                        result.add(atributo);
                        // Marcar este filtro como procesado
                        processedFiltroIds.add(filtroDato.getFiltro().getId());
                        break;
                    }
                }
            }
        });

        // Agregar los atributos restantes ordenados alfabéticamente
        validAtributos.stream()
            .filter(a -> !processedFiltroIds.contains(a.getFiltro().getId()))
            .sorted((a1, a2) -> {
                String nombre1 = a1.getFiltro().getNombreFiltro();
                String nombre2 = a2.getFiltro().getNombreFiltro();
                return nombre1.compareToIgnoreCase(nombre2);
            })
            .forEach(result::add);

        return result;
    }

    /**
     * Estima el ancho total disponible para la tabla
     * @return Ancho estimado en píxeles
     */
    private double getEstimatedTableWidth() {
        // Si la tabla ya está visible y tiene un ancho, usamos ese valor
        if (tvItems.isVisible() && tvItems.getWidth() > 0) {
            return tvItems.getWidth();
        }

        // Si el contenedor raíz tiene un ancho, usamos ese valor
        if (root.getWidth() > 0) {
            return root.getWidth();
        }

        // Si no podemos obtener un valor real, usamos un valor estimado
        return 1000; // Valor por defecto
    }

    /**
     * Limpia los recursos al cerrar la vista
     */
    @Override
    public void onClose() {
        // Llamar al método onClose de la clase base para limpiar las suscripciones
        super.onClose();

        // Limpiar los items para liberar memoria
        if (tvItems != null) {
            tvItems.getItems().clear();
        }

        // Limpiar referencias a objetos
        producto = null;
        searchProductGui = null;

        // Registrar el cierre del controlador
        log.info("ProductoItemSearchedController cerrado y recursos liberados.");
    }


}
