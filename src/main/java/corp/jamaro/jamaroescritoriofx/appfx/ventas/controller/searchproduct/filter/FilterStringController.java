package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct.filter;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.FiltroDatoRellenado;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.SearchProductGui;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.SearchProductGuiService;
import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.layout.AnchorPane;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.CustomTextField;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.net.URL;
import java.util.Objects;
import java.util.ResourceBundle;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class FilterStringController extends BaseController {

    @FXML
    private AnchorPane anchorPaneRoot;

    @FXML
    private CustomTextField txtStringData;

    private final SearchProductGuiService searchProductGuiService;

    @Setter
    private FiltroDatoRellenado filterData;
    @Setter
    private SearchProductGui searchProductGui;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // Permite que el campo se expanda horizontalmente.
        txtStringData.setMaxWidth(Double.MAX_VALUE);
        // Actualiza el dato al presionar ENTER.
        txtStringData.setOnAction(event -> updateFilterData());
        // Actualiza también al perder el foco.
        txtStringData.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal) {
                updateFilterData();
            }
        });
    }

    /**
     * Configura este componente con el filtro actual y el SearchProductGui asociado.
     * Inicializa el campo de texto con el dato actual y coloca un Label a la izquierda con el nombre del filtro.
     */
    public void setFilterData(FiltroDatoRellenado filterData, SearchProductGui searchProductGui) {
        this.filterData = filterData;
        this.searchProductGui = searchProductGui;
        txtStringData.setText(filterData.getDato() != null ? filterData.getDato() : "");
        if (filterData.getFiltro() != null) {
            Label leftLabel = new Label(filterData.getFiltro().getNombreFiltro());
            txtStringData.setLeft(leftLabel);
        }
    }

    /**
     * Envía la actualización del dato del filtro al servidor si ha cambiado.
     * Tras la actualización, posiciona el caret al final del texto.
     */
    private void updateFilterData() {
        String newValue = txtStringData.getText();
        // Normalizamos: si es null o solo espacios se trata como null; de lo contrario se quitan espacios laterales.
        String normalizedNew = (newValue != null && !newValue.trim().isEmpty()) ? newValue.trim() : null;
        String normalizedOld = (filterData.getDato() != null && !filterData.getDato().trim().isEmpty()) ? filterData.getDato().trim() : null;
        if (filterData != null && !Objects.equals(normalizedNew, normalizedOld)) {
            Mono<?> updateMono = searchProductGuiService.updateFiltroDato(
                    searchProductGui.getId(),
                    filterData.getFila(),
                    filterData.getColumna(),
                    normalizedNew
            );
            subscribeOnBoundedElastic(
                    updateMono,
                    updatedGui -> {
                        log.info("Filtro actualizado: fila={}, columna={}, nuevo dato={}",
                                filterData.getFila(), filterData.getColumna(), normalizedNew);
                        runOnUiThread(() -> txtStringData.positionCaret(txtStringData.getText().length()));
                    },
                    error -> log.error("Error actualizando filtro: {}", error.getMessage(), error)
            );
        }
    }
}
