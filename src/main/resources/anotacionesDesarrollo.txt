- Doble filtro en busqueda por ejemplo estoy Buscando rodajes entonces tengo int ext alt para rellenar
pero hay veces en las que necesita más de un rodaje entonces puedo agregar otra vez otro int ext alt debajo de los filtros
y hacer un OR con esos para que los busque también así puedo buscar dos o más rodajes a la vez

- Entender que en los tipos de filtro realmente no hay boolean sino algo que es unico entonces si yo selecciono con seguro es por que tiene seguro
pero si no lo selecciono simplemente no lo tiene, no es necesario tratarlo como boolean por que ocuparía un true y un false en base de datos

# ATAJOS DE TECLADO DEL SISTEMA DE VENTAS

## UniversalSaleGuiController (Nivel Superior)
- Sin atajos específicos implementados actualmente

## MainSaleGuiController (Gestión de Tabs de Ventas)
- **Ctrl+F1**: Enfocar txtCodigo del primer tab de venta (equivalente a Ctrl+1)
- **Ctrl+1 a Ctrl+9**: Enfocar tab de venta específico (1-9) y dirigir foco al txtCodigo del SaleController
- **Ctrl+0**: Eliminar todos los tabs de SaleGui usando endpoint bulk (evita deadlocks)

## SaleGuiController (Ventana de Venta Individual)
- **F1**: Enfocar txtCodigo del SaleController (solo F1, sin modificadores)
- **Alt+C**: Enfocar txtDocumentoNombreRazon del SaleController
- **Alt+S**: Cerrar este tab de SaleGui
- **Alt+B**: Agregar nueva búsqueda (tab de SearchProductGui)
- **Alt+V**: Vender (delegar a SaleController)
- **Alt+P**: Imprimir (delegar a SaleController)
- **Alt+L**: Limpiar (delegar a SaleController)
- **Ctrl+L**: Limpiar (mantener compatibilidad, delegar a SaleController)
- **Alt+1 a Alt+9**: Enfocar tab de búsqueda específico (1-9) y dirigir foco al txtSearch del SearchFilterController
- **Alt+0**: Eliminar todos los tabs de SearchProductGui usando endpoint bulk (evita deadlocks)

## SaleController (Controlador de Venta)
- Los atajos se manejan desde SaleGuiController y se delegan a los métodos públicos:
  - handleBtnVender() - para Alt+V
  - handleBtnImprimir() - para Alt+P
  - handleBtnLimpiar() - para Alt+L y Ctrl+L
- **Búsqueda de Clientes en txtDocumentoNombreRazon**:
  - Autocompletado inteligente con búsqueda por documento (numérico) o nombre (texto)
  - Al presionar Enter: busca cliente existente o ofrece crear uno nuevo
  - Creación automática de clientes con datos básicos según el tipo de entrada
  - Integración completa con ClienteService (searchByDocument, searchByName, createCliente)

## SearchProductGuiController y SearchFilterController (Búsqueda de Productos)
- Los atajos se manejan desde SaleGuiController para enfocar el txtSearch

## Jerarquía de Atajos:
1. **Ctrl+**: Operaciones a nivel de MainSaleGuiController (gestión de tabs de ventas)
   - **Ctrl+F1**: Enfocar txtCodigo del primer tab (equivalente a Ctrl+1)
   - **Ctrl******: Enfocar tabs de venta específicos y enfocar txtCodigo
   - **Ctrl+0**: Eliminar todos los tabs de venta usando endpoint bulk
2. **Alt+**: Operaciones a nivel de SaleGuiController (dentro de una venta específica)
   - **Alt******: Enfocar tabs de búsqueda específicos
   - **Alt+0**: Eliminar todos los tabs de búsqueda
   - **Alt+C**: Enfocar campo de cliente
   - **Alt+S**: Cerrar tab de venta
   - **Alt+B**: Nueva búsqueda
   - **Alt+V/P/L**: Acciones de venta
3. **F1 solo**: Campo principal de entrada (txtCodigo) - solo funciona en SaleGuiController
4. **Sin colisiones**: F1 solo vs Ctrl+F1 están diferenciados para evitar conflictos

## Notas Técnicas:
- Todos los atajos funcionan globalmente dentro de su contexto respectivo
- Se utiliza Platform.runLater() para operaciones de UI
- Los atajos respetan el patrón servidor-como-fuente-de-verdad
- **Eliminaciones bulk**: Se usan endpoints específicos (removeAllSaleGuis, deleteAllSearchProducts) para evitar deadlocks
- Los enfoques de campos se realizan localmente sin afectar otros clientes
- **Búsqueda de clientes**: Integración completa con autocompletado, búsqueda inteligente y creación automática
- **ClienteService actualizado**: Incluye métodos createCliente y updateCliente para gestión completa de clientes
- **Componente reutilizable de clientes**: ClienteCreationController y ClienteCreationHelper para crear/editar clientes
  - FXML: src/main/resources/fxml/cliente/clienteCreation.fxml
  - Controller: ClienteCreationController con handlers funcionales (ClienteCreatedHandler, ClienteUpdatedHandler, etc.)
  - Helper: ClienteCreationHelper para facilitar el uso con diálogos modales
  - Integrado en SaleController para crear/editar clientes con menú contextual (clic derecho)
  - Patrón reutilizable similar a SearchProductGuiController con interfaces funcionales


- aun hay errores en CONTROL + F1 mejorar noesta funcionando