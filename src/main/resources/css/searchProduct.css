/*
 * JavaFX CSS - Hoja de estilos específica para la funcionalidad de búsqueda de productos
 * Este archivo contiene estilos para:
 * - SearchProductGuiController
 * - ProductoItemSearchedController
 * - productoItemSearched.fxml
 * - searchProductGui.fxml
 */

/* Importar variables de tamaños de fuente */
@import "font-sizes.css";

/* Importar variables globales de colores desde styles.css */
@import "styles.css";

/* ===== Estilos para el ListView principal de productos ===== */
.productos-list-view {
    -fx-background-color: -fx-primary-color;
    -fx-padding: 3px 3px 3px 3px; /* Reducir el padding para dar más espacio a los elementos */
    -fx-border-width: 0;
}

/* Ocultar la barra de desplazamiento horizontal */
.productos-list-view .scroll-bar:horizontal {
    -fx-opacity: 0;
    -fx-pref-height: 0;
    -fx-min-height: 0;
    -fx-max-height: 0;
}

.productos-list-view .list-cell {
    -fx-padding: 1px 1px 1px 1px; /* Reducir el padding para dar más espacio a los elementos */
    -fx-wrap-text: true; /* Permitir que el texto se ajuste */
}

.productos-list-view .list-cell:filled {
    -fx-border-width: 0 0 1px 0;
    -fx-border-color: derive(-fx-primary-color-dark, 15%);
}

/* Estilo para celdas vacías - fondo transparente o del mismo color que el ListView */
.productos-list-view .list-cell:empty {
    -fx-background-color: -fx-primary-color;
    -fx-border-width: 0;
    -fx-opacity: 0;
    -fx-mouse-transparent: true;
    -fx-padding: 0;
}

.productos-list-view .list-cell:odd:filled {
    -fx-background-color: derive(-fx-primary-color-dark, 15%);
}

.productos-list-view .list-cell:even:filled {
    -fx-background-color: derive(-fx-primary-color-light, -20%);
}

.productos-list-view .list-cell:filled:hover {
    -fx-background-color: derive(-fx-secondary-color-light, 20%);
    -fx-transition: all 0.05s ease-out;
}

.productos-list-view .list-cell:filled:selected {
    -fx-background-color: derive(-fx-secondary-color-dark, -10%);
    -fx-border-color: -fx-secondary-color;
    -fx-border-width: 0 0 1px 0;
}

/* ===== Estilos para ProductoItemSearched ===== */
.producto-item {
    -fx-background-color: inherit; /* Hereda el color de fondo de la celda */
    -fx-border-color: -fx-secondary-color-dark;
    -fx-border-width: 0 0 1px 0;
    -fx-border-radius: 3px;
    -fx-background-radius: 3px;
    -fx-padding: 4px 12px 4px 8px; /* Reducir el padding vertical para minimizar la altura */
    -fx-cursor: hand;
    -fx-effect: dropshadow(three-pass-box, -fx-shadow-color, 3, 0, 0, 0);
    -fx-transition: all 0.1s ease-out; /* Transición más rápida y suave */
    -fx-wrap-text: true; /* Permitir que el texto se ajuste */
}

/* Estilos para el código del producto */
.producto-codigo {
    -fx-font-weight: bold;
    -fx-font-size: 15px;
    -fx-text-fill: -fx-secondary-color;
    -fx-background-color: -fx-primary-color;
    -fx-padding: 5px 8px;
    -fx-background-radius: 3px;
}

/* Estilos para la descripción del producto */
.producto-descripcion {
    -fx-font-weight: bold;
    -fx-font-size: 14px;
    -fx-text-fill: -fx-light-color;
    /* No necesitamos text-transform ya que aplicamos camel case en el controlador */
}

.producto-item:hover {
    -fx-background-color: -fx-secondary-color-light;
    -fx-effect: dropshadow(three-pass-box, -fx-shadow-color, 5, 0, 0, 2);
    -fx-border-color: -fx-secondary-color;
    -fx-transition: all 0.05s ease-out; /* Transición aún más rápida para hover */
}

/* Estilo para el producto en una celda seleccionada */
.productos-list-view .list-cell:filled:selected .producto-item {
    -fx-background-color: derive(-fx-secondary-color-dark, -10%);
    -fx-border-color: -fx-secondary-color;
    -fx-border-width: 0 0 1px 0;
    -fx-effect: dropshadow(three-pass-box, -fx-shadow-color, 8, 0, 0, 3);
}

/* ===== Estilos para los items dentro de ProductoItemSearched ===== */
.producto-item .items-table-view {
    -fx-background-color: derive(-fx-primary-color-light, -5%);
    -fx-border-color: -fx-secondary-color;
    -fx-border-width: 1px;
    -fx-border-radius: 3px;
    -fx-padding: 3px;
    -fx-effect: innershadow(three-pass-box, -fx-shadow-color, 3, 0, 0, 0);
    -fx-max-height: 0px;
    -fx-opacity: 0;
    -fx-transition: all 0.08s ease-out; /* Transición más rápida */
    -fx-visibility: hidden;
    /* Altura fija para cada celda - debe coincidir con el valor en adjustTableViewHeight() */
    -fx-fixed-cell-size: 25px;
    /* Asegurar que las columnas se ajusten correctamente */
    -fx-column-resize-policy: constrained-resize;
}

.producto-item-expanded .items-table-view {
    -fx-max-height: 2000px; /* Valor alto para permitir cualquier altura, incluso con muchos items */
    -fx-opacity: 1;
    -fx-visibility: visible;
    -fx-transition: all 0.08s ease-in; /* Transición aún más rápida para la expansión */
}

/* Estilo para el producto expandido - mejor feedback visual */
.producto-item-expanded {
    -fx-background-color: derive(-fx-secondary-color-light, -15%); /* Color ligeramente más oscuro que el hover */
    -fx-border-color: -fx-secondary-color;
    -fx-border-width: 0 0 1px 0;
    -fx-effect: dropshadow(three-pass-box, -fx-shadow-color, 8, 0, 0, 3);
}

/* Ocultar la barra de desplazamiento */
.producto-item .items-table-view .scroll-bar:vertical {
    -fx-opacity: 0;
    -fx-pref-width: 0;
    -fx-min-width: 0;
    -fx-max-width: 0;
}

/* Estilos para las columnas del TableView */
.items-table-view .column-header {
    -fx-background-color: -fx-secondary-color-dark;
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold;
    -fx-padding: 5px;
    -fx-border-color: derive(-fx-secondary-color-dark, 60%);
    -fx-border-width: 0 1px 1px 0;
    -fx-font-size: 12px;
    -fx-alignment: center;
}

/* Estilo para la columna principal de atributos */
.items-table-view .atributos-column > .column-header-background {
    -fx-background-color: derive(-fx-secondary-color-dark, -10%);
    -fx-font-weight: bold;
    -fx-font-size: 13px;
}

/* Estilo para las subcolumnas de atributos */
.items-table-view .column-header-background .nested-column-header .column-header {
    -fx-background-color: derive(-fx-secondary-color-dark, 5%);
    -fx-border-color: derive(-fx-secondary-color-dark, 60%);
    -fx-border-width: 0 1px 1px 0;
}

.items-table-view .column-header-background {
    -fx-background-color: -fx-secondary-color-dark;
}

/* Eliminar el borde de la cabecera de la tabla */
.items-table-view .column-header-background .filler {
    -fx-background-color: -fx-secondary-color-dark;
}

/* Eliminar el borde de la cabecera de la tabla */
.items-table-view .corner {
    -fx-background-color: -fx-secondary-color-dark;
}

/* Estilo para las filas de la tabla */
.items-table-view .table-row-cell {
    -fx-background-color: derive(-fx-secondary-color-dark, 85%);
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 1px;
    -fx-text-fill: -fx-light-color;
}

/* Estilo para las filas al pasar el mouse */
.items-table-view .table-row-cell:hover {
    -fx-background-color: derive(-fx-secondary-color, 50%);
    -fx-transition: -fx-hover-transition;
}

/* Estilo para las filas seleccionadas */
.items-table-view .table-row-cell:selected {
    -fx-background-color: derive(-fx-secondary-color, 30%);
    -fx-text-fill: -fx-light-color;
}

/* Estilo para filas con stock cero o negativo */
.items-table-view .table-row-cell.zero-stock-row {
    -fx-background-color: -fx-error-color;
}

/* Estilo para filas con stock cero o negativo al pasar el mouse */
.items-table-view .table-row-cell.zero-stock-row:hover {
    -fx-background-color: derive(-fx-error-color, 20%);
}

/* Estilo para filas con stock cero o negativo seleccionadas */
.items-table-view .table-row-cell.zero-stock-row:selected {
    -fx-background-color: derive(-fx-error-color, -20%);
}

/* Estilo para las celdas */
.items-table-view .table-cell {
    -fx-alignment: center;
    -fx-padding: 2px 5px;
    -fx-border-color: derive(-fx-secondary-color-dark, 60%);
    -fx-border-width: 0 1px 1px 0;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

/* Eliminar el borde de foco */
.items-table-view:focused {
    -fx-background-color: transparent;
    -fx-background-insets: 0;
    -fx-background-radius: 0;
    -fx-border-color: -fx-secondary-color;
    -fx-border-width: 1px;
    -fx-border-radius: 3px;
}

/* Eliminar el borde de foco de las celdas */
.items-table-view .table-row-cell:filled:selected .table-cell {
    -fx-background-color: transparent;
}

/* Estilo para el scroll */
.items-table-view .scroll-bar:horizontal {
    -fx-opacity: 0;
    -fx-pref-height: 0;
    -fx-min-height: 0;
    -fx-max-height: 0;
}

/* Estilos para el icono de expansión */
.producto-item .font-icon-light {
    -fx-icon-color: -fx-secondary-color;
    -fx-cursor: hand;
    -fx-transition: all 0.1s ease-in-out;
}

.producto-item-expanded .font-icon-light {
    -fx-icon-color: -fx-secondary-color-light;
}

/* ===== Estilos para los códigos de fábrica ===== */
.codigo-fabrica-label {
    -fx-background-color: derive(-fx-secondary-color-dark, -10%);
    -fx-background-radius: 3px;
    -fx-padding: 2px 4px; /* Reducir el padding para hacer el componente más compacto */
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold;
    -fx-font-size: 11px;
}

/* ===== Estilos para los vehículos ===== */
.vehiculos-label {
    -fx-font-size: 13px;
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold; /* Añadir negrita según solicitud del usuario */
    -fx-text-transform: uppercase; /* Asegurar que el texto esté en mayúsculas según preferencia del usuario */
    -fx-alignment: center-left; /* Alineación a la izquierda */
    -fx-padding: 1px 0 1px 5px; /* Reducir el padding vertical para minimizar la altura */
}

/* Estilo específico para vbVehiculos según preferencias del usuario */
.vb-vehiculos .label {
    -fx-font-size: 13px;
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold; /* Añadir negrita según solicitud del usuario */
    -fx-text-transform: uppercase; /* Texto siempre en mayúsculas según preferencia del usuario */
    -fx-alignment: center-left; /* Texto alineado a la izquierda */
}

/* ===== Estilos para los atributos ===== */
.atributo-box {
    -fx-background-color: derive(-fx-primary-color-light, -5%);
    -fx-background-radius: 3px;
    -fx-padding: 3px 6px;
}

.atributo-nombre {
    -fx-font-weight: bold;
    -fx-text-fill: -fx-light-grey;
    -fx-font-size: 14px; /* Aumentar tamaño según preferencia del usuario */
}

.atributo-valor {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 14px; /* Aumentar tamaño según preferencia del usuario */
}

/* ===== Estilos para los items ===== */
.item-content {
    -fx-background-radius: 2px;
    -fx-padding: 1px 4px;
    -fx-border-color: -fx-secondary-color-dark;
    -fx-border-width: 0 0 0 1px;
    -fx-border-radius: 0 0 0 1px;
    -fx-effect: none;
}

.item-code {
    -fx-font-size: 12px;
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold;
}

.item-price {
    -fx-font-weight: bold;
    -fx-text-fill: -fx-info-color;
    -fx-font-size: 11px;
    -fx-background-color: derive(-fx-primary-color-dark, 10%);
    -fx-background-radius: 2px;
    -fx-padding: 0px 2px;
}

/* ===== Estilos para mensajes en el TableView ===== */
.loading-label {
    -fx-font-size: 13px;
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold;
    -fx-padding: 10px;
}

.no-items-label {
    -fx-font-size: 13px;
    -fx-text-fill: -fx-warning-color;
    -fx-font-weight: bold;
    -fx-padding: 10px;
}

.error-label {
    -fx-font-size: 13px;
    -fx-text-fill: -fx-error-color;
    -fx-font-weight: bold;
    -fx-padding: 10px;
}
