/* JavaFX CSS - Hoja de estilos principal mejorada para la aplicación */

/* Importar variables de tamaños de fuente */
@import "font-sizes.css";

/*
 * Definición de variables de color, tamaño de fuente y estilo
 * (Estas variables se usan a lo largo del CSS para mantener consistencia.)
 */
* {
    /* Colores base */
    -fx-light-color: #dbdce1;
    -fx-primary-color: #2b2b2b; /* color de las lineas gruesas se paraciones, relleno de tables, titulo tabs, etc*/
    -fx-secondary-color: #115f68;
    -fx-light-grey: #df956d;
    -fx-dark-grey: #2a2abf;

    /* Colores derivados */
    -fx-primary-color-light: #3a3a3a;/*el fondos de las tablas de los textfield, de los menus, en general el color de fondo  */
    -fx-primary-color-dark: #090801;
    -fx-secondary-color-light: #3ca0aa;/* color de los hover del mouse sobre botones,*/
    -fx-secondary-color-dark: #333e7e;

    /* Colores adicionales */
    -fx-info-color: #3498db;
    -fx-warning-color: #f39c12;
    -fx-error-color: #e74c3c;
    -fx-success-color: #2ecc71;
    -fx-accent-color: #3ca0aa; /* Alias para -fx-secondary-color-light para compatibilidad */

    /* Variables para sombras y efectos */
    -fx-shadow-color: rgba(131, 3, 3, 0.2);
    -fx-hover-transition: 0.15s;

    /* Variables de estilo */
    -fx-default-border-radius: 3px;
    -fx-default-padding: 6px 12px 6px 12px;
}

/* Estilos generales */
.root {
    -fx-font-family: 'Segoe UI', Arial, sans-serif;
    -fx-base: -fx-primary-color;
    -fx-background: -fx-primary-color-dark;
    -fx-control-inner-background: -fx-primary-color-light;
    -fx-accent: -fx-secondary-color;
    -fx-focus-color: -fx-secondary-color;
    -fx-faint-focus-color: -fx-secondary-color-light;
}

/* Etiquetas (Label) */
.label {
    -fx-text-fill: -fx-light-grey;
    -fx-font-weight: bold;
}

/* Botones */
.button {
    -fx-background-color: -fx-secondary-color !important;
    -fx-text-fill: -fx-light-color !important; /* Usamos -fx-light-color en lugar de blanco (#ffffff) */
    -fx-background-radius: 3px;
    -fx-border-radius: 3px;
    -fx-font-weight: bold;
    -fx-padding: 3px 12px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, -fx-shadow-color, 5, 0, 0, 1);
    -fx-transition: -fx-hover-transition;
}

.button:hover {
    -fx-background-color: -fx-secondary-color-light !important;
    -fx-text-fill: -fx-light-color !important;
    -fx-effect: dropshadow(gaussian, -fx-shadow-color, 18, 0, 0, 2);
}

.button:pressed {
    -fx-background-color: -fx-secondary-color-dark !important;
    -fx-text-fill: -fx-light-color !important;
    -fx-effect: dropshadow(gaussian, -fx-shadow-color, 3, 0, 0, 0);
}

/* Estilos específicos para botones de navegación */
.nav-button {
    -fx-background-color: -fx-secondary-color !important;
    -fx-text-fill: -fx-light-color !important;
}

.nav-button:hover {
    -fx-background-color: -fx-secondary-color-light !important;
    -fx-text-fill: -fx-light-color !important;
}

/*
 * Clase adicional para FontIcon:
 * -fx-icon-color es la propiedad que Ikonli usa para el color del ícono.
 */
.font-icon-light {
    -fx-icon-color: -fx-light-color; /* Aplica el color definido en -fx-light-color */
}

/* Campos de texto */
.text-field, .custom-text-field {
    -fx-background-color: -fx-control-inner-background;
    -fx-background-radius: 3px;
    -fx-border-color: -fx-primary-color-light;
    -fx-border-radius: 3px;
    -fx-prompt-text-fill: derive(-fx-light-grey, -30%);
    -fx-padding: 6px 8px;
    -fx-effect: innershadow(three-pass-box, -fx-shadow-color, 5, 0, 0, 0);
}

.text-field:focused, .custom-text-field:focused {
    -fx-border-color: -fx-secondary-color;
    -fx-effect: innershadow(three-pass-box, -fx-secondary-color, 5, 0, 0, 0);
}

/* ListView */
.list-view {
    -fx-background-color: -fx-control-inner-background;
    -fx-border-color: -fx-primary-color-light;
    -fx-effect: dropshadow(three-pass-box, -fx-shadow-color, 5, 0, 0, 0);
}

.list-cell {
    -fx-background-color: -fx-control-inner-background;
    -fx-padding: 8px 12px;
    -fx-transition: -fx-hover-transition;
    -fx-effect: dropshadow(three-pass-box, -fx-shadow-color, 3, 0, 0, 0);
}

.list-cell:hover {
    -fx-background-color: derive(-fx-primary-color-light, -10%);
    -fx-effect: dropshadow(three-pass-box, -fx-shadow-color, 8, 0, 0, 2);
}

/* Estilos para BienServicioCargado */
.stock-error {
    -fx-background-color: -fx-error-color;
}

.stock-error:hover {
    -fx-background-color: derive(-fx-error-color, 20%);
}

/* Celdas del TreeTableView */
.tree-table-cell {
    -fx-font-size: 12px;   /* Tamaño de la fuente */
    -fx-font-weight: bold;            /* Negrita */
    -fx-text-fill: -fx-light-color;   /* Usa -fx-light-color */
}

/* Cabecera de columnas en TreeTableView */
.tree-table-view .column-header .label {
    -fx-font-size: 14px;
    -fx-text-fill: -fx-light-color;
}

/*
 * Cuando la fila padre está expandida, se le aplica la pseudo-clase ":expanded"
 * y se modifica el color de fondo con un derivado de -fx-light-grey.
 */
.tree-table-row-cell.parent-row:expanded {
    -fx-background-color: derive(-fx-light-grey, -30%);
}

/* TreeTableView: filas padres e hijos */
.tree-table-row-cell.parent-row {
    -fx-background-color: -fx-primary-color-dark;
    -fx-text-fill: -fx-light-color;
}

.tree-table-row-cell.child-row {
    -fx-background-color: -fx-primary-color-light;
    -fx-text-fill: -fx-light-color;
}

/* Selección en TreeTableView */
.tree-table-row-cell.parent-row:selected {
    -fx-background-color: -fx-secondary-color-dark;
    -fx-text-fill: -fx-light-color;
}

.tree-table-row-cell.child-row:selected {
    -fx-background-color: -fx-secondary-color-dark;
    -fx-text-fill: -fx-light-color;
}

/* Hover en TreeTableView */
.tree-table-row-cell.parent-row:hover {
    -fx-background-color: derive(-fx-secondary-color-light, 20%);
}

.tree-table-row-cell.child-row:hover {
    -fx-background-color: derive(-fx-secondary-color, 10%);
}

/*
 * Los estilos para la funcionalidad de búsqueda de productos se han movido a un archivo separado:
 * src/main/resources/css/searchProduct.css
 *
 * Esto incluye estilos para:
 * - SearchProductGuiController
 * - ProductoItemSearchedController
 * - productoItemSearched.fxml
 * - searchProductGui.fxml
 */

/* Barra de desplazamiento en TreeTableView */
.tree-table-view .scroll-bar:vertical .thumb {
    -fx-background-color: -fx-secondary-color;
    -fx-background-radius: 3px;
    -fx-transition: -fx-hover-transition;
}

.tree-table-view .scroll-bar:vertical .thumb:hover {
    -fx-background-color: -fx-secondary-color-light;
}

/* Flechas de expandir/contraer en TreeTableView */
.tree-table-view .tree-disclosure-node > .arrow {
    -fx-fill: -fx-secondary-color;
    -fx-background-color: -fx-secondary-color;
    -fx-scale-x: 1.5;
    -fx-scale-y: 1.5;
}

/* ToolTip */
.tooltip {
    -fx-background-color: -fx-primary-color-dark;
    -fx-text-fill: -fx-light-grey;
    -fx-font-size: 12px;
    -fx-padding: 5px 10px;
    -fx-background-radius: 3px;
    -fx-effect: dropshadow(three-pass-box, -fx-shadow-color, 5, 0, 0, 2);
}

/* Menú (básico) */
.menu-bar {
    -fx-background-color: -fx-primary-color-dark !important;
}

.menu-bar .menu-button {
    -fx-background-color: -fx-primary-color-dark !important;
}

.menu-bar .menu-button:hover,
.menu-bar .menu-button:showing {
    -fx-background-color: -fx-secondary-color !important;
}

.menu-bar .menu-button .label {
    -fx-text-fill: -fx-light-grey !important;
}

.menu-bar .menu-button:hover .label,
.menu-bar .menu-button:showing .label {
    -fx-text-fill: -fx-light-color !important;
}

.menu-item {
    -fx-background-color: -fx-primary-color-dark !important;
}

.menu-item:focused {
    -fx-background-color: -fx-secondary-color !important;
}

.menu-item:focused .label {
    -fx-text-fill: -fx-light-color !important;
}

/* ScrollPane */
.scroll-pane {
    -fx-background-color: -fx-primary-color;
    -fx-border-color: -fx-primary-color-light;
}

.scroll-pane > .viewport {
    -fx-background-color: -fx-primary-color;
}

/* Separator */
.separator:horizontal .line {
    -fx-border-color: -fx-primary-color-light transparent transparent transparent;
    -fx-border-width: 1px;
}

/* ===== Estilos Modernos para Diálogos Sin Decoraciones ===== */
.dialog-pane {
    -fx-background-color: -fx-primary-color;
    -fx-border-color: -fx-secondary-color;
    -fx-border-width: 1px;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.5), 20, 0.4, 0, 8);
    -fx-padding: 0;
    -fx-min-width: 280px;
    -fx-pref-width: 320px;
    -fx-max-width: 380px;
}

/* Ocultar el header completamente */
.dialog-pane .header-panel {
    -fx-pref-height: 0;
    -fx-min-height: 0;
    -fx-max-height: 0;
    -fx-padding: 0;
    -fx-background-color: transparent;
}

/* Estilo moderno para el contenido */
.dialog-pane .content {
    -fx-background-color: -fx-primary-color;
    -fx-padding: 15px 20px;
    -fx-background-radius: 10px 10px 0 0;
    -fx-spacing: 12px;
}

.dialog-pane .content .label {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
    -fx-wrap-text: true;
    -fx-text-alignment: left;
}

/* Estilos específicos para labels de diálogos */
.dialog-title-label {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 13px;
    -fx-font-weight: 600;
    -fx-padding: 0 0 8px 0;
}

.dialog-field-label {
    -fx-text-fill: -fx-secondary-color-light;
    -fx-font-size: 11px;
    -fx-font-weight: 700;
    -fx-min-width: 80px;
}

.dialog-field-value {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 11px;
    -fx-font-weight: 500;
}

/* ComboBox moderno en diálogos */
.dialog-pane .combo-box {
    -fx-background-color: -fx-primary-color-light;
    -fx-border-color: -fx-secondary-color-light;
    -fx-border-width: 1.5px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-pref-width: 180px;
    -fx-min-width: 180px;
    -fx-min-height: 32px;
}

.dialog-pane .combo-box:focused {
    -fx-border-color: -fx-secondary-color;
    -fx-border-width: 2px;
}

.dialog-pane .combo-box .list-cell {
    -fx-text-fill: -fx-light-color;
    -fx-background-color: transparent;
    -fx-padding: 6px 12px;
    -fx-font-size: 12px;
}

.dialog-pane .combo-box .list-cell:selected {
    -fx-background-color: -fx-secondary-color;
    -fx-text-fill: -fx-light-color;
}

/* TextField moderno en diálogos */
.dialog-pane .text-field {
    -fx-background-color: -fx-primary-color-light;
    -fx-text-fill: -fx-light-color;
    -fx-border-color: -fx-secondary-color-light;
    -fx-border-width: 1.5px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 8px 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
    -fx-pref-width: 120px;
    -fx-min-height: 32px;
    -fx-text-alignment: center;
}

.dialog-pane .text-field:focused {
    -fx-border-color: -fx-secondary-color;
    -fx-border-width: 2px;
}

/* Área de botones moderna */
.dialog-pane .button-bar {
    -fx-background-color: derive(-fx-primary-color, -8%);
    -fx-padding: 12px 20px;
    -fx-spacing: 10px;
    -fx-background-radius: 0 0 10px 10px;
    -fx-alignment: center-right;
}

.dialog-pane .button-bar .button {
    -fx-background-color: -fx-secondary-color;
    -fx-text-fill: -fx-light-color;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-font-weight: 600;
    -fx-padding: 8px 18px;
    -fx-cursor: hand;
    -fx-font-size: 11px;
    -fx-min-width: 75px;
    -fx-min-height: 32px;
}

.dialog-pane .button-bar .button:hover {
    -fx-background-color: -fx-secondary-color-light;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 3, 0.2, 0, 1);
}

.dialog-pane .button-bar .button:pressed {
    -fx-background-color: -fx-secondary-color-dark;
    -fx-effect: none;
}

/* Estilos específicos para VBox y HBox en diálogos */
.dialog-pane .content .vbox,
.dialog-pane .content .hbox {
    -fx-spacing: 12px;
    -fx-alignment: center-left;
}

/* Mejorar la visibilidad del texto en diálogos */
.dialog-pane .content .text {
    -fx-fill: -fx-light-color;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
}

/* Estilos para el placeholder del TextField */
.dialog-pane .text-field .text {
    -fx-fill: -fx-light-color;
}

.dialog-pane .text-field:focused .text {
    -fx-fill: -fx-light-color;
}

/* Mejorar contraste del ComboBox */
.dialog-pane .combo-box .text {
    -fx-fill: -fx-light-color;
    -fx-font-weight: 500;
}

.dialog-pane .combo-box .arrow-button {
    -fx-background-color: transparent;
}

.dialog-pane .combo-box .arrow {
    -fx-background-color: -fx-light-color;
}

/* Estilos específicos para el contenido del texto en diálogos */
.dialog-pane .content .text-flow {
    -fx-text-alignment: left;
    -fx-line-spacing: 2px;
}

.dialog-pane .content .text-flow .text {
    -fx-fill: -fx-light-color;
    -fx-font-size: 14px;
    -fx-font-weight: 400;
}

/* Mejorar el prompt text del TextField */
.dialog-pane .text-field .prompt-text {
    -fx-fill: derive(-fx-light-color, -40%);
    -fx-font-style: italic;
    -fx-font-size: 13px;
}

/* Estilos para el texto seleccionado en TextField */
.dialog-pane .text-field .text {
    -fx-highlight-fill: -fx-info-color;
    -fx-highlight-text-fill: -fx-light-color;
}

/* Mejorar la visibilidad del texto del ComboBox */
.dialog-pane .combo-box .text {
    -fx-fill: -fx-light-color;
    -fx-font-weight: 600;
}

/* Asegurar que el texto del ComboBox sea visible */
.dialog-pane .combo-box .list-cell .text {
    -fx-fill: -fx-light-color;
}

.dialog-pane .combo-box > .list-cell {
    -fx-text-fill: -fx-light-color;
}

/* Mejorar la flecha del ComboBox */
.dialog-pane .combo-box .arrow-button {
    -fx-background-color: transparent;
    -fx-padding: 8px;
}

.dialog-pane .combo-box .arrow {
    -fx-background-color: -fx-light-color;
    -fx-shape: "M4,8 L8,4 L12,8 L4,8";
    -fx-scale-x: 1.2;
    -fx-scale-y: 1.2;
}

/* Botón por defecto (Aceptar) */
.dialog-pane .button-bar .button:default {
    -fx-background-color: -fx-info-color;
}

.dialog-pane .button-bar .button:default:hover {
    -fx-background-color: derive(-fx-info-color, 15%);
}

.separator:vertical .line {
    -fx-border-color: transparent -fx-primary-color-light transparent transparent;
    -fx-border-width: 1px;
}

/* Estilos para TableView */
.table-view {
    -fx-background-color: -fx-primary-color-light;
    -fx-table-cell-border-color: -fx-primary-color-dark;
}

.table-view .column-header-background {
    -fx-background-color: -fx-primary-color-dark;
}

.table-view .column-header .label {
    -fx-text-fill: -fx-light-grey;
    -fx-font-weight: bold;
}

.table-row-cell:selected {
    -fx-background-color: -fx-secondary-color;
    -fx-text-fill: -fx-light-color;
}

/* Pestañas (TabPane) */
.tab-pane .tab-header-area {
    -fx-background-color: -fx-primary-color-dark;
}

.tab-pane .tab-header-area .tab .tab-label {
    -fx-text-fill: -fx-light-grey;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
}

/* ===== Estilos globales para barras de desplazamiento ===== */
/* Estos estilos se aplicarán a todas las barras de desplazamiento de la aplicación */

/* Estilo para el track (fondo) de la barra de desplazamiento */
.scroll-bar:vertical .track,
.scroll-bar:horizontal .track {
    -fx-background-color: derive(-fx-primary-color-dark, 15%);
    -fx-border-color: derive(-fx-primary-color-dark, 25%);
    -fx-background-radius: 0;
    -fx-border-radius: 0;
}

/* Estilo para el thumb (control deslizante) de la barra de desplazamiento */
.scroll-bar:vertical .thumb,
.scroll-bar:horizontal .thumb {
    -fx-background-color: -fx-secondary-color;
    -fx-background-radius: 4px;
    -fx-border-radius: 4px;
    -fx-effect: dropshadow(three-pass-box, rgba(0, 0, 0, 0.3), 3, 0, 0, 0);
}

/* Estilo para el thumb al pasar el mouse */
.scroll-bar:vertical .thumb:hover,
.scroll-bar:horizontal .thumb:hover {
    -fx-background-color: -fx-secondary-color-light;
    -fx-cursor: hand;
}

/* Estilo para el thumb al presionar */
.scroll-bar:vertical .thumb:pressed,
.scroll-bar:horizontal .thumb:pressed {
    -fx-background-color: derive(-fx-secondary-color-dark, 10%);
}

/* Estilo para los botones de incremento y decremento */
.scroll-bar:vertical .increment-button,
.scroll-bar:vertical .decrement-button,
.scroll-bar:horizontal .increment-button,
.scroll-bar:horizontal .decrement-button {
    -fx-background-color: derive(-fx-primary-color-dark, 10%);
    -fx-background-radius: 0;
    -fx-padding: 3px;
}

/* Estilo para los botones al pasar el mouse */
.scroll-bar:vertical .increment-button:hover,
.scroll-bar:vertical .decrement-button:hover,
.scroll-bar:horizontal .increment-button:hover,
.scroll-bar:horizontal .decrement-button:hover {
    -fx-background-color: derive(-fx-primary-color-dark, 20%);
    -fx-cursor: hand;
}

/* Estilo para las flechas de los botones */
.scroll-bar:vertical .increment-arrow,
.scroll-bar:vertical .decrement-arrow,
.scroll-bar:horizontal .increment-arrow,
.scroll-bar:horizontal .decrement-arrow {
    -fx-background-color: -fx-light-color;
    -fx-effect: dropshadow(three-pass-box, rgba(0, 0, 0, 0.3), 2, 0, 0, 0);
    -fx-shape: "M4,8 L8,4 L12,8 L4,8";
    -fx-padding: 0.25em;
    -fx-rotate: 0;
}

/* Rotación específica para cada flecha */
.scroll-bar:vertical .increment-arrow {
    -fx-rotate: 0;
}

.scroll-bar:vertical .decrement-arrow {
    -fx-rotate: 180;
}

.scroll-bar:horizontal .increment-arrow {
    -fx-rotate: -90;
}

.scroll-bar:horizontal .decrement-arrow {
    -fx-rotate: 90;
}

/* Estilo para el corner (esquina entre barras horizontal y vertical) */
.scroll-bar:vertical .corner,
.scroll-bar:horizontal .corner {
    -fx-background-color: derive(-fx-primary-color-dark, 15%);
}
