<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<?import javafx.scene.text.Text?>
<?import org.controlsfx.control.textfield.CustomTextField?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<AnchorPane prefHeight="926.0" prefWidth="1269.0" stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.producto.controller.CreacioProducto">
   <children>
      <VBox spacing="3.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>
            <HBox spacing="3.0">
               <children>
                  <VBox spacing="3.0">
                     <children>
                        <VBox>
                           <children>
                              <HBox>
                                 <children>
                                    <CustomTextField fx:id="txtNombrePrincipal" editable="false" focusTraversable="false" minHeight="42.0" promptText="Nombre Del Producto" HBox.hgrow="ALWAYS">
                                       <font>
                                          <Font size="18.0" />
                                       </font>
                                    </CustomTextField>
                                    <Button fx:id="btnAddNombre" focusTraversable="false" minHeight="42.0" minWidth="33.0" mnemonicParsing="false" text="+">
                                       <font>
                                          <Font size="18.0" />
                                       </font>
                                    </Button>
                                    <Button fx:id="btnBuscarVehiculo" focusTraversable="false" mnemonicParsing="false" onAction="#onBtnBuscarVehiculoClick" prefHeight="42.0">
                                       <graphic>
                                          <FontIcon iconColor="WHITE" iconLiteral="fas-search" />
                                       </graphic>
                                       <HBox.margin>
                                          <Insets left="3.0" />
                                       </HBox.margin>
                                    </Button>
                                 </children>
                              </HBox>
                              <ListView fx:id="lvNombres" prefHeight="72.0" prefWidth="1224.0" />
                           </children>
                        </VBox>
                        <HBox spacing="3.0">
                           <children>
                              <VBox>
                                 <children>
                                    <HBox>
                                       <children>
                                          <CustomTextField fx:id="txtCategoria" minHeight="42.0" promptText="Categorias o Grupos (kits, familias, etc)" HBox.hgrow="ALWAYS">
                                             <font>
                                                <Font size="18.0" />
                                             </font>
                                          </CustomTextField>
                                          <Button fx:id="btnAddCategoria" focusTraversable="false" minHeight="42.0" minWidth="33.0" mnemonicParsing="false" text="+">
                                             <font>
                                                <Font size="18.0" />
                                             </font>
                                          </Button>
                                       </children>
                                    </HBox>
                                    <ListView fx:id="lvCategorias" prefHeight="72.0" prefWidth="1224.0" />
                                 </children>
                              </VBox>
                              <VBox>
                                 <children>
                                    <HBox>
                                       <children>
                                          <CustomTextField fx:id="txtNombreVehiculo" minHeight="42.0" promptText="Vehiculos" HBox.hgrow="ALWAYS">
                                             <font>
                                                <Font size="18.0" />
                                             </font>
                                          </CustomTextField>
                                          <Button fx:id="btnAddVehiculo" focusTraversable="false" minHeight="42.0" minWidth="33.0" mnemonicParsing="false" text="+">
                                             <font>
                                                <Font size="18.0" />
                                             </font>
                                          </Button>
                                       </children>
                                    </HBox>
                                    <ListView fx:id="lvVehiculos" prefHeight="72.0" prefWidth="1224.0" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  <VBox>
                     <children>
                        <ImageView fx:id="imageProducto" fitHeight="192.0" fitWidth="256.0" pickOnBounds="true" preserveRatio="true" />
                        <HBox alignment="CENTER" spacing="9.0" VBox.vgrow="ALWAYS">
                           <children>
                              <Button fx:id="btnBackImage" focusTraversable="false" mnemonicParsing="false" text="&lt;" HBox.hgrow="ALWAYS" />
                              <Button fx:id="btnAddImage" focusTraversable="false" minWidth="71.0" mnemonicParsing="false" onAction="#onBtnAddImageClick" text="Agregar" />
                              <Button fx:id="btnDeleteImagen" focusTraversable="false" minWidth="71.0" mnemonicParsing="false" text="Eliminar" />
                              <Button fx:id="btnNextImage" focusTraversable="false" mnemonicParsing="false" text="&gt;" HBox.hgrow="ALWAYS" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
            </HBox>
            <VBox VBox.vgrow="ALWAYS">
               <children>
                  <Separator prefWidth="200.0" />
                  <Label alignment="CENTER" maxWidth="1.7976931348623157E308">
                     <graphic>
                        <Text fill="WHITE" strokeType="OUTSIDE" strokeWidth="0.0" text="ATRIBUTOS DE CATEGORIA" textAlignment="CENTER">
                           <font>
                              <Font size="18.0" />
                           </font>
                        </Text>
                     </graphic>
                  </Label>
                  <Separator prefWidth="200.0" />
                  <HBox VBox.vgrow="ALWAYS" />
               </children>
            </VBox>
            <VBox VBox.vgrow="ALWAYS">
               <children>
                  <Separator prefWidth="200.0" />
                  <Label alignment="CENTER" maxWidth="1.7976931348623157E308">
                     <graphic>
                        <Text fill="WHITE" strokeType="OUTSIDE" strokeWidth="0.0" text="FILTROS DE VEHICULO" textAlignment="CENTER">
                           <font>
                              <Font size="18.0" />
                           </font>
                        </Text>
                     </graphic>
                  </Label>
                  <Separator prefWidth="200.0" />
                  <HBox VBox.vgrow="ALWAYS" />
               </children>
            </VBox>
         </children>
      </VBox>
   </children>
</AnchorPane>
