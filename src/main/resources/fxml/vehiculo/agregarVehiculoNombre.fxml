<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<?import org.controlsfx.control.textfield.CustomTextField?>

<!-- Ajusta la ruta del CSS a la tuya si difiere -->

<AnchorPane stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller.AgregarVehiculoNombre">

   <children>
      <VBox AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>

            <!-- Campo de texto para ingresar la nueva Marca -->
            <CustomTextField fx:id="txtVehiculoNombre" onAction="#onBtnAgregarClick" prefHeight="29.0" prefWidth="540.0" promptText="Vehiculo Nombre" VBox.vgrow="NEVER">
               <VBox.margin>
                  <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
               </VBox.margin>
               <font>
                  <Font size="18.0" />
               </font></CustomTextField>
            <ListView fx:id="lvCoincidencias" prefHeight="180.0" />

            <!-- Botonera inferior -->
            <HBox alignment="CENTER_RIGHT" spacing="9.0">
               <children>
                  <Button fx:id="btnAgregar" minWidth="72.0" mnemonicParsing="false" onAction="#onBtnAgregarClick" text="Agregar" />
                  <Button fx:id="btnLimpiar" minWidth="72.0" mnemonicParsing="false" onAction="#onBtnLimpiarClick" text="Limpiar" />
                  <Button fx:id="btnSalir" minWidth="72.0" mnemonicParsing="false" onAction="#onBtnSalirClick" text="Salir" />
               </children>
               <VBox.margin>
                  <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
               </VBox.margin>
            </HBox>

         </children>
      </VBox>
   </children>
</AnchorPane>
