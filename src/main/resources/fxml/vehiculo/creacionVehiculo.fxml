<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ListView?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<?import org.controlsfx.control.SearchableComboBox?>
<?import org.controlsfx.control.textfield.CustomTextField?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<AnchorPane minWidth="603.0" prefWidth="997.0" stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller.CreacionVehiculoController">
   <children>
      <VBox layoutX="14.0" layoutY="53.0" spacing="9.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>
            <HBox spacing="3.0">
               <VBox.margin>
                  <Insets />
               </VBox.margin>
               <children>
                  <CustomTextField fx:id="txtNombreVehiculo" alignment="CENTER" focusTraversable="false" minHeight="30.0" prefHeight="42.0" promptText="Nombre del Vehículo" HBox.hgrow="ALWAYS">
                     <font>
                        <Font name="System Bold" size="18.0" />
                     </font></CustomTextField>
                  <Button fx:id="btnAddNombreVehiculo" focusTraversable="false" minHeight="42.0" minWidth="33.0" mnemonicParsing="false" onAction="#onBtnAddNombreVehiculoClick" text="+">
                     <font>
                        <Font size="18.0" />
                     </font></Button>
                  <Button fx:id="btnBuscarVehiculo" focusTraversable="false" mnemonicParsing="false" onAction="#onBtnBuscarVehiculoClick" prefHeight="42.0">
                     <graphic>
                        <FontIcon iconColor="WHITE" iconLiteral="fas-search" />
                     </graphic>
                  </Button>
               </children>
            </HBox>
            <ListView fx:id="lvNombreVehiculos" focusTraversable="false" prefHeight="120.0" VBox.vgrow="ALWAYS" />
            <HBox alignment="CENTER_LEFT" spacing="9.0" VBox.vgrow="NEVER">
               <children>
                  <VBox spacing="9.0" HBox.hgrow="ALWAYS">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="9.0">
                           <children>
                              <HBox alignment="CENTER">
                                 <children>
                                    <Label alignment="CENTER_RIGHT" focusTraversable="false" minWidth="45.0" text="Marca">
                                       <HBox.margin>
                                          <Insets right="9.0" />
                                       </HBox.margin>
                                    </Label>
                                    <SearchableComboBox fx:id="cmbMarca" minHeight="36.0" minWidth="161.0" prefHeight="36.0" prefWidth="154.0" />
                                    <Button fx:id="btnMarca" focusTraversable="false" minHeight="36.0" mnemonicParsing="false" onAction="#onBtnMarcaClick" text="+" />
                                 </children>
                              </HBox>
                              <HBox alignment="CENTER" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label alignment="CENTER_RIGHT" focusTraversable="false" minWidth="42.0" text="Modelo">
                                       <HBox.margin>
                                          <Insets right="9.0" />
                                       </HBox.margin>
                                    </Label>
                                    <SearchableComboBox fx:id="cmbModelo" minHeight="36.0" minWidth="270.0" prefWidth="432.0" HBox.hgrow="ALWAYS" />
                                    <Button fx:id="btnAddModeloMarca" focusTraversable="false" minHeight="36.0" minWidth="33.0" mnemonicParsing="false" onAction="#onBtnAddModeloMarcaClick" text="+" />
                                 </children>
                              </HBox>
                           </children>
                           <VBox.margin>
                              <Insets />
                           </VBox.margin>
                        </HBox>
                        <HBox alignment="CENTER_LEFT" spacing="9.0">
                           <children>
                              <HBox alignment="CENTER" spacing="9.0">
                                 <children>
                                    <Label alignment="CENTER_RIGHT" focusTraversable="false" minWidth="45.0" text="Año" />
                                    <CustomTextField fx:id="txtAnioInicio" alignment="CENTER" minHeight="36.0" minWidth="51.0" prefHeight="36.0" prefWidth="51.0" promptText="desde" HBox.hgrow="ALWAYS" />
                                    <CustomTextField fx:id="txtAnioFinal" alignment="CENTER" minHeight="36.0" minWidth="51.0" prefHeight="36.0" prefWidth="51.0" promptText="hasta" HBox.hgrow="ALWAYS" />
                                 </children>
                              </HBox>
                              <HBox alignment="CENTER" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label alignment="CENTER_RIGHT" focusTraversable="false" text="Motor">
                                       <HBox.margin>
                                          <Insets right="9.0" />
                                       </HBox.margin>
                                    </Label>
                                    <SearchableComboBox fx:id="cmbMotor" minHeight="36.0" minWidth="120.0" prefHeight="36.0" prefWidth="120.0" HBox.hgrow="ALWAYS" />
                                    <Button fx:id="btnAddMotorMarca" focusTraversable="false" minHeight="36.0" mnemonicParsing="false" onAction="#onBtnAddMotorMarcaClick" text="+" />
                                 </children>
                              </HBox>
                              <HBox alignment="CENTER" spacing="9.0">
                                 <children>
                                    <Label alignment="CENTER_RIGHT" focusTraversable="false" text="Cilindrada" />
                                    <SearchableComboBox fx:id="cmbCilindrada" minHeight="36.0" minWidth="145.0" prefHeight="36.0" prefWidth="90.0" />
                                 </children>
                              </HBox>
                           </children>
                        </HBox>
                        <Separator prefWidth="200.0" />
                        <HBox alignment="CENTER_LEFT" prefHeight="36.0" prefWidth="542.0" spacing="9.0">
                           <children>
                              <HBox alignment="CENTER" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label alignment="CENTER_RIGHT" focusTraversable="false" minWidth="45.0" text="Versión">
                                       <HBox.margin>
                                          <Insets right="9.0" />
                                       </HBox.margin>
                                    </Label>
                                    <SearchableComboBox fx:id="cmbVersion" minHeight="36.0" minWidth="81.0" prefHeight="36.0" prefWidth="84.0" HBox.hgrow="ALWAYS" />
                                    <Button fx:id="btnAddVersionMarca" focusTraversable="false" minHeight="36.0" mnemonicParsing="false" onAction="#onBtnAddVersionMarcaClick" text="+" />
                                 </children>
                              </HBox>
                              <HBox alignment="CENTER" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label alignment="CENTER_RIGHT" focusTraversable="false" text="Carroceria">
                                       <HBox.margin>
                                          <Insets />
                                       </HBox.margin>
                                       <padding>
                                          <Insets right="18.0" />
                                       </padding>
                                    </Label>
                                    <SearchableComboBox fx:id="cmbCarroceria" minHeight="36.0" minWidth="120.0" prefHeight="36.0" prefWidth="120.0" HBox.hgrow="ALWAYS" />
                                 </children>
                              </HBox>
                           </children>
                        </HBox>
                        <HBox alignment="CENTER_LEFT" prefHeight="36.0" prefWidth="542.0" spacing="9.0">
                           <children>
                              <HBox alignment="CENTER" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label alignment="CENTER_RIGHT" focusTraversable="false" minWidth="45.0" text="Tracción">
                                       <HBox.margin>
                                          <Insets right="9.0" />
                                       </HBox.margin>
                                    </Label>
                                    <SearchableComboBox fx:id="cmbTraccion" minHeight="36.0" minWidth="120.0" prefHeight="36.0" prefWidth="120.0" HBox.hgrow="ALWAYS" />
                                 </children>
                              </HBox>
                              <HBox alignment="CENTER" HBox.hgrow="ALWAYS">
                                 <children>
                                    <Label alignment="CENTER_RIGHT" focusTraversable="false" text="Transmisión">
                                       <HBox.margin>
                                          <Insets />
                                       </HBox.margin>
                                       <padding>
                                          <Insets right="9.0" />
                                       </padding>
                                    </Label>
                                    <SearchableComboBox fx:id="cmbTransimision" minHeight="36.0" minWidth="120.0" prefHeight="36.0" prefWidth="120.0" HBox.hgrow="ALWAYS" />
                                 </children>
                              </HBox>
                           </children>
                        </HBox>
                        <HBox alignment="CENTER_LEFT" spacing="9.0" VBox.vgrow="ALWAYS">
                           <children>
                              <HBox alignment="CENTER" spacing="9.0">
                                 <children>
                                    <Button fx:id="btnCrearVehiculo" focusTraversable="false" minHeight="30.0" minWidth="78.0" mnemonicParsing="false" onAction="#onBtnCrearVehiculoClick" text="Guardar" />
                                    <Button fx:id="btnEliminarVehiculo" focusTraversable="false" minHeight="30.0" minWidth="78.0" mnemonicParsing="false" onAction="#onBtnEliminarVehiculoClick" text="Eliminar" />
                                    <Button fx:id="btnLimpiar" focusTraversable="false" minHeight="30.0" minWidth="78.0" mnemonicParsing="false" onAction="#onBtnLimpiarClick" text="Limpiar" />
                                    <Button fx:id="btnSalir" focusTraversable="false" minHeight="30.0" minWidth="78.0" mnemonicParsing="false" onAction="#onBtnSalirClick" text="Salir" />
                                 </children>
                              </HBox>
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  <VBox>
                     <children>
                        <ImageView fx:id="imgVehiculo" fitHeight="192.0" fitWidth="256.0" pickOnBounds="true" preserveRatio="true" />
                        <HBox alignment="CENTER" spacing="9.0">
                           <children>
                              <Button fx:id="btnBackImage" focusTraversable="false" mnemonicParsing="false" text="&lt;" HBox.hgrow="ALWAYS" />
                              <Button fx:id="btnAddImage" focusTraversable="false" minWidth="71.0" mnemonicParsing="false" onAction="#onBtnAddImageClick" text="Agregar" />
                              <Button fx:id="btnDeleteImagen" focusTraversable="false" minWidth="71.0" mnemonicParsing="false" text="Eliminar" />
                              <Button fx:id="btnNextImage" focusTraversable="false" mnemonicParsing="false" text="&gt;" HBox.hgrow="ALWAYS" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
            </HBox>
         </children>
         <padding>
            <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
         </padding>
      </VBox>
   </children>
</AnchorPane>
