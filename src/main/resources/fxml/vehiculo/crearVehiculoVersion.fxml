<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<?import org.controlsfx.control.textfield.CustomTextField?>

<!-- Ajusta la ruta del CSS a la tuya si difiere -->

<AnchorPane prefHeight="450.0" prefWidth="540.0" stylesheets="@../../css/styles.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller.CrearVehiculoVersion">

   <children>
      <VBox prefHeight="200.0" prefWidth="100.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>

            <!-- Campo de texto para ingresar la nueva Marca -->
            <CustomTextField fx:id="txtVersion" minHeight="45.0" promptText="Vehiculo Versión" VBox.vgrow="NEVER">
               <VBox.margin>
                  <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
               </VBox.margin>
               <font>
                  <Font size="18.0" />
               </font></CustomTextField>
            <TextArea fx:id="txtDescripcion" prefHeight="200.0" prefWidth="200.0" promptText="Descripción">
               <font>
                  <Font name="System Bold" size="12.0" />
               </font>
            </TextArea>

            <!-- Contenedor con scroll para la tabla de resultados -->
            <ScrollPane fitToHeight="true" fitToWidth="true" VBox.vgrow="ALWAYS">
               <content>
                  <!-- Tabla donde se listarán todas las marcas -->
                  <TableView fx:id="tvResultados">
                     <!-- Definimos una columna para mostrar el nombre de la marca -->
                     <columns>
                        <TableColumn fx:id="colVersion" prefWidth="403.0" text="Vehiculo Modelo" />
                     </columns>
                  </TableView>
               </content>
            </ScrollPane>

            <!-- Botonera inferior -->
            <HBox alignment="CENTER_RIGHT" prefHeight="100.0" prefWidth="200.0" spacing="9.0">
               <children>
                  <Button fx:id="btnGuardar" minWidth="72.0" mnemonicParsing="false" onAction="#onBtnGuardarClick" text="Guardar" />
                  <Button fx:id="btnLimpiar" minWidth="72.0" mnemonicParsing="false" onAction="#onBtnLimpiarClick" text="Limpiar" />
                  <Button fx:id="btnSalir" minWidth="72.0" mnemonicParsing="false" onAction="#onBtnSalirClick" text="Salir" />
               </children>
               <VBox.margin>
                  <Insets bottom="9.0" left="9.0" right="9.0" top="9.0" />
               </VBox.margin>
            </HBox>

         </children>
      </VBox>
   </children>
</AnchorPane>
