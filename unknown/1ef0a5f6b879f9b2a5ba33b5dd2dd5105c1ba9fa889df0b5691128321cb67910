<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane fx:id="rootBienServicioCargado" maxHeight="65.0" minHeight="65.0" onMouseClicked="#handleClick" prefHeight="65.0" styleClass="bien-servicio-cargado" stylesheets="@../../css/bienServicioCargado.css" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.BienServicioCargadoController">
   <children>
      <VBox fx:id="vbDatosItem" alignment="CENTER" maxWidth="90.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>
            <Label fx:id="lblGrupoItem" alignment="CENTER" maxWidth="90.0" minWidth="90.0" text="Rodaje" VBox.vgrow="ALWAYS">
               <font>
                  <Font size="12.0" />
               </font>
               <VBox.margin>
                  <Insets top="3.0" />
               </VBox.margin>
            </Label>
            <Label fx:id="lblCodCompuesto" alignment="CENTER" maxWidth="90.0" minWidth="90.0" prefHeight="45.0" prefWidth="90.0" styleClass="bien-servicio-codigo" text="00158KOY" textAlignment="CENTER" VBox.vgrow="ALWAYS">
               <font>
                  <Font size="15.0" />
               </font>
            </Label>
            <Label fx:id="lblMarca" alignment="CENTER" maxWidth="90.0" minWidth="90.0" styleClass="marca-label" text="Koyo" VBox.vgrow="ALWAYS">
               <font>
                  <Font size="12.0" />
               </font>
               <VBox.margin>
                  <Insets bottom="3.0" />
               </VBox.margin>
            </Label>
         </children>
      </VBox>

      <!-- Panel central responsivo: Descripción editable con overlay -->
      <StackPane fx:id="stackDescripcion" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="90.0" AnchorPane.rightAnchor="90.0" AnchorPane.topAnchor="0.0">
         <children>
            <Label fx:id="descripcionDelBienServicio" maxWidth="Infinity" prefWidth="90.0" styleClass="bien-servicio-descripcion" text="Descripcion Producto" wrapText="true" StackPane.alignment="CENTER">
               <font>
                  <Font size="14.0" />
               </font>
               <padding>
                  <Insets left="3.0" right="3.0" />
               </padding>
            </Label>
            <!-- TextField overlay para edición -->
            <TextField fx:id="txtDescripcionEdit" maxWidth="Infinity" styleClass="edit-overlay" visible="false" StackPane.alignment="CENTER">
               <font>
                  <Font size="14.0" />
               </font>
            </TextField>
         </children>
      </StackPane>

      <AnchorPane fx:id="anchorPrecioCantidad" maxHeight="65.0" maxWidth="90.0" minHeight="65.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>
            <!-- Precio Acordado con overlay -->
            <StackPane AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="33.0" AnchorPane.topAnchor="0.0">
               <children>
                  <Label fx:id="precioAcordado" alignment="CENTER" styleClass="bien-servicio-precio" text="3.00" StackPane.alignment="CENTER">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </Label>
                  <TextField fx:id="txtPrecioEdit" alignment="CENTER" styleClass="edit-overlay" visible="false" StackPane.alignment="CENTER">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </TextField>
               </children>
            </StackPane>

            <!-- Cantidad con overlay -->
            <StackPane prefHeight="24.0" prefWidth="41.0" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="58.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <Label fx:id="cantidad" alignment="CENTER" styleClass="bien-servicio-cantidad" text="001" StackPane.alignment="CENTER">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </Label>
                  <TextField fx:id="txtCantidadEdit" alignment="CENTER" styleClass="edit-overlay" visible="false" StackPane.alignment="CENTER">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </TextField>
               </children>
            </StackPane>

            <!-- Monto Acordado con overlay -->
            <StackPane AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="24.0">
               <children>
                  <Label fx:id="montoAcordado" alignment="CENTER" styleClass="bien-servicio-total" text="3.00" StackPane.alignment="CENTER">
                     <font>
                        <Font size="21.0" />
                     </font>
                  </Label>
                  <TextField fx:id="txtMontoEdit" alignment="CENTER" styleClass="edit-overlay" visible="false" StackPane.alignment="CENTER">
                     <font>
                        <Font size="21.0" />
                     </font>
                  </TextField>
               </children>
            </StackPane>
         </children>
      </AnchorPane>
   </children>
</AnchorPane>
